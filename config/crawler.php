<?php

return [
    /*
    |--------------------------------------------------------------------------
    | API Keys
    |--------------------------------------------------------------------------
    |
    | API keys for various services used by the crawler.
    |
    */
    'google_api_key' => env('CRAWLER_GOOGLE_API'),
    'partner_ads_api_key' => env('CRAWLER_PARTNER_ADS_API'),
    'adtraction_api_key' => env('CRAWLER_ADTRACTION_API'),
    'awin_api_key' => env('CRAWLER_AWIN_API'),
    'kelkoo_api_key' => env('CRAWLER_KELKOO_API'),
    'brave_search_api_key' => env('CRAWLER_BRAVE_SEARCH_API'),
    'serpapi_api_key' => env('CRAWLER_SERPAPI_API'),
    'serpstack_api_key' => env('CRAWLER_SERPSTACK_API'),
    'brightdata_api_key' => env('CRAWLER_BRIGHTDATA_API_KEY'),
    'brightdata_customer_id' => env('CRAWLER_BRIGHTDATA_CUSTOMER_ID'),
    'brightdata_zone_id' => env('CRAWLER_BRIGHTDATA_ZONE_ID'),

    /*
    |--------------------------------------------------------------------------
    | Curanet Domain Availability Service (DAS)
    |--------------------------------------------------------------------------
    |
    | Configuration for the Curanet Domain Availability Service API.
    |
    */
    'curanet_das' => [
        'base_url' => env('CURANET_DAS_API_BASE_URL'),
        'client_id' => env('CURANET_DAS_API_CLIENT_ID'),
        'client_secret' => env('CURANET_DAS_API_SECRET'),
    ],
];
