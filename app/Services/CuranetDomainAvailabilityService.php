<?php

namespace App\Services;

use App\Integrations\DomainService\CuranetDasApiClient;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CuranetDomainAvailabilityService
{
    /**
     * The DAS API client instance.
     *
     * @var \App\Integrations\DomainService\CuranetDasApiClient
     */
    protected $apiClient;

    /**
     * Cache duration in minutes.
     *
     * @var int
     */
    protected $cacheDuration = 60; // 1 hour

    /**
     * Create a new domain availability service instance.
     */
    public function __construct(CuranetDasApiClient $apiClient)
    {
        $this->apiClient = $apiClient;
    }

    /**
     * Check if a domain is available.
     */
    public function checkDomain(string $domain): array
    {
        $cacheKey = "curanet_domain_availability:{$domain}";

        return Cache::remember($cacheKey, $this->cacheDuration, function () use ($domain) {
            $result = $this->apiClient->checkDomainAvailability([$domain]);

            if (! $result['complete'] || empty($result['dasResults'])) {
                Log::warning("Curanet domain check failed for: {$domain}");

                return [
                    'domain' => $domain,
                    'available' => false,
                    'status' => 'Error',
                    'error' => 'Failed to check domain availability',
                ];
            }

            $domainResult = $result['dasResults'][0];

            return [
                'domain' => $domainResult['domain'],
                'available' => $domainResult['status'] === 'Available',
                'status' => $domainResult['status'],
                'error' => $domainResult['errorMessage'] ?? null,
            ];
        });
    }

    /**
     * Check multiple domains availability (up to 10 domains).
     */
    public function checkMultipleDomains(array $domains): array
    {
        // Limit to 10 domains as per API constraints
        $domains = array_slice($domains, 0, 10);

        // Check if all domains are cached
        $results = [];
        $domainsToCheck = [];

        foreach ($domains as $domain) {
            $cacheKey = "curanet_domain_availability:{$domain}";

            if (Cache::has($cacheKey)) {
                $results[$domain] = Cache::get($cacheKey);
            } else {
                $domainsToCheck[] = $domain;
            }
        }

        // If there are domains that need to be checked via API
        if (! empty($domainsToCheck)) {
            $apiResults = $this->apiClient->checkDomainAvailability($domainsToCheck);

            if ($apiResults['complete'] && ! empty($apiResults['dasResults'])) {
                foreach ($apiResults['dasResults'] as $domainResult) {
                    $domain = $domainResult['domain'];
                    $result = [
                        'domain' => $domain,
                        'available' => $domainResult['status'] === 'Available',
                        'status' => $domainResult['status'],
                        'error' => $domainResult['errorMessage'] ?? null,
                    ];

                    // Cache the result
                    $cacheKey = "curanet_domain_availability:{$domain}";
                    Cache::put($cacheKey, $result, $this->cacheDuration);

                    $results[$domain] = $result;
                }
            } else {
                // Handle API failure
                Log::warning('Curanet domain check failed for multiple domains', ['domains' => $domainsToCheck]);
                foreach ($domainsToCheck as $domain) {
                    $results[$domain] = [
                        'domain' => $domain,
                        'available' => false,
                        'status' => 'Error',
                        'error' => 'Failed to check domain availability',
                    ];
                }
            }
        }

        return $results;
    }

    /**
     * Set the cache duration.
     *
     * @return $this
     */
    public function setCacheDuration(int $minutes)
    {
        $this->cacheDuration = $minutes;

        return $this;
    }
}
