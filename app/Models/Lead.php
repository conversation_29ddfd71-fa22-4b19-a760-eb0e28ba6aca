<?php

namespace App\Models;

use App\Helpers\DomainParserHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Lead extends Model
{
    use HasFactory;

    protected $fillable = [
        'website_status',
        'website_ssl',
        'website_url',
        'website_technologies',
        'website_platform_name',
        'website_platform_version',
        'website_theme_name',
        'website_theme_version',
        'website_powered_by',
        'website_hreflang',
        'website_phone',
        'website_email',
        'website_vat',
        'website_title',
        'website_meta_description',
        'website_ip',
        'website_made_by',
        'website_type',
        'domain',
        'tld',
        'domain_status',
        'domain_created',
        'domain_expires',
        'domain_owner',
        'company_number',
        'comment',
        'google_pagespeed_desktop_performance_score',
        'google_pagespeed_desktop_accessibility_score',
        'google_pagespeed_desktop_seo_score',
        'google_pagespeed_desktop_best_practices_score',
        'google_pagespeed_desktop_screenshot',
        'google_pagespeed_mobile_performance_score',
        'google_pagespeed_mobile_accessibility_score',
        'google_pagespeed_mobile_seo_score',
        'google_pagespeed_mobile_best_practices_score',
        'google_pagespeed_mobile_screenshot',
        'contact',
        'relevant',
        'manual',
        'deleted',
        'data_updated',
        'change',
    ];

    protected $casts = [
        'change' => 'datetime',
        'contact' => 'boolean',
        'created_at' => 'date',
        'data_updated' => 'datetime',
        'deleted' => 'boolean',
        'domain_created' => 'date',
        'domain_expires' => 'date',
        'domain_status' => 'boolean',
        'google_pagespeed_desktop_accessibility_score' => 'integer',
        'google_pagespeed_desktop_best_practices_score' => 'integer',
        'google_pagespeed_desktop_performance_score' => 'integer',
        'google_pagespeed_desktop_seo_score' => 'integer',
        'google_pagespeed_mobile_accessibility_score' => 'integer',
        'google_pagespeed_mobile_best_practices_score' => 'integer',
        'google_pagespeed_mobile_performance_score' => 'integer',
        'google_pagespeed_mobile_seo_score' => 'integer',
        'manual' => 'boolean',
        'relevant' => 'boolean',
        'updated_at' => 'date',
        'website_platform_name' => 'string',
        'website_platform_version' => 'string',
        'website_theme_name' => 'string',
        'website_theme_version' => 'string',
        'website_url' => 'string',
        'website_ssl' => 'boolean',
        'website_status' => 'boolean',
        'website_technologies' => 'array',
        'website_type' => 'boolean',
        'website_powered_by' => 'string',
        'website_hreflang' => 'string',
        'website_phone' => 'string',
        'website_email' => 'string',
        'website_vat' => 'string',
        'website_title' => 'string',
        'website_meta_description' => 'string',
        'website_made_by' => 'string',
    ];

    /**
     * Get the company that owns the lead.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_number', 'number');
    }

    /**
     * Set the domain and automatically extract and set the TLD using DomainParserHelper.
     */
    public function setDomainAttribute(?string $value): void
    {
        $this->attributes['domain'] = $value;
        // Use the helper to extract TLD
        $domainInfo = DomainParserHelper::getProcessedDomainInformation($value);
        $this->attributes['tld'] = $domainInfo ? $domainInfo['tld'] : null;
    }
}
