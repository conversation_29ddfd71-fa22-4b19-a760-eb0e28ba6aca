<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Company extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'companies';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'number',
        'name',
        'status',
        'protected',
        'address',
        'zipcode',
        'city',
        'phone',
        'email',
        'employees',
        'industry_code',
        'company_code',
        'owners',
        'date_start',
        'date_end',
    ];

    /**
     * The attributes that should be appended to the model's array form.
     *
     * @var array
     */
    protected $appends = ['name_with_number'];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date_start' => 'datetime', // Was start_date (timestamp)
        'date_end' => 'datetime', // New, for end_date (timestamp)
    ];

    /**
     * Get the leads associated with the company.
     */
    public function leads(): HasMany
    {
        return $this->hasMany(Lead::class, 'company_number', 'number'); // Renamed foreign key from vat_id
    }

    /**
     * Get the company's name concatenated with its number.
     */
    public function getNameWithNumberAttribute(): string
    {
        return $this->name.' ('.$this->number.')';
    }
}
