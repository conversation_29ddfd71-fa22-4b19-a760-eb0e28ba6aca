<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class WebAnalyzerHelper
{
    private $technologies = [];

    public function __construct()
    {
        $this->fetchTechnologies();
    }

    private function fetchTechnologies()
    {
        $characters = array_merge(range('a', 'z'), ['_']); // a-z + _

        foreach ($characters as $char) {
            $cacheKey = 'WebAnalyzerHelper_technologies_'.$char;
            $url = "https://raw.githubusercontent.com/enthec/webappanalyzer/refs/heads/main/src/technologies/{$char}.json";

            // Check if the data is already cached
            if (Cache::has($cacheKey)) {
                // Get the cached data
                $data = Cache::get($cacheKey);
            } else {
                // Fetch the JSON from the GitHub repository
                $response = Http::get($url);

                // Check for successful response
                if ($response->successful()) {
                    $data = $response->json();
                    // Cache the fetched data for 1 hour
                    Cache::put($cacheKey, $data, now()->addDay());
                } else {
                    $data = null;  // Return null if the request fails
                }
            }

            // Add the data to the technologies array
            if ($data) {
                $this->technologies = array_merge($this->technologies, $data);
            }
        }

        return $this->technologies;
    }

    private function detectTechnologies($html, $headers = [], $cookies = [], $url = '', $robotsTxt = '')
    {
        $detectedTechnologies = [];

        foreach ($this->technologies as $techName => $techData) {
            // Check JavaScript patterns
            if (isset($techData['js'])) {
                foreach ($techData['js'] as $jsPattern => $value) {
                    if (preg_match('/'.preg_quote($jsPattern, '/').'/', $html)) {
                        $detectedTechnologies[$techName] = $techData;
                        break;
                    }
                }
            }

            // Check DOM (HTML) patterns
            if (isset($techData['dom'])) {
                foreach ($techData['dom'] as $domSelector => $domProperties) {
                    if (str_contains($html, $domSelector)) {
                        if (isset($domProperties['attributes'])) {
                            foreach ($domProperties['attributes'] as $attr => $pattern) {
                                if (! preg_match('/'.preg_quote($pattern, '/').'/', $html)) {
                                    continue 2;
                                }
                            }
                        }
                        if (isset($domProperties['text']) && ! str_contains($html, $domProperties['text'])) {
                            continue;
                        }
                        $detectedTechnologies[$techName] = $techData;
                        break;
                    }
                }
            }

            // Check HTTP Headers patterns
            if (isset($techData['headers'])) {
                foreach ($techData['headers'] as $headerName => $headerPattern) {
                    // Normalize header name from techData for lookup, as HTTP header keys are often normalized (e.g., to lowercase).
                    $lookupHeaderName = strtolower($headerName);

                    if (isset($headers[$lookupHeaderName])) {
                        $headerContent = $headers[$lookupHeaderName]; // This can be a string or an array of strings

                        // Ensure we are iterating over an array of values for uniform processing
                        $valuesToTest = is_array($headerContent) ? $headerContent : [$headerContent];

                        foreach ($valuesToTest as $singleHeaderValue) {
                            // Ensure the value is a string before passing to preg_match
                            // Added 'i' modifier for case-insensitive regex matching.
                            if (is_string($singleHeaderValue) && preg_match('/'.preg_quote($headerPattern, '/').'/i', $singleHeaderValue)) {
                                $detectedTechnologies[$techName] = $techData;
                                // Technology detected by this header rule.
                                // Break from iterating over $valuesToTest (if multiple values for one header)
                                // AND break from iterating over $techData['headers'] (other header rules for this tech).
                                break 2;
                            }
                        }
                    }
                }
            }

            // Check Cookie patterns
            if (isset($techData['cookies'])) {
                foreach ($techData['cookies'] as $cookieName => $cookiePattern) {
                    if (isset($cookies[$cookieName]) && preg_match('/'.preg_quote($cookiePattern, '/').'/', $cookies[$cookieName])) {
                        $detectedTechnologies[$techName] = $techData;
                        break;
                    }
                }
            }

            // Check CSS rules patterns
            if (isset($techData['css'])) {
                foreach ((array) $techData['css'] as $cssPattern) {
                    if (preg_match('/'.preg_quote($cssPattern, '/').'/', $html)) {
                        $detectedTechnologies[$techName] = $techData;
                        break;
                    }
                }
            }

            // Check Text patterns
            if (isset($techData['text'])) {
                foreach ((array) $techData['text'] as $textPattern) {
                    if (preg_match('/'.preg_quote($textPattern, '/').'/', $html)) {
                        $detectedTechnologies[$techName] = $techData;
                        break;
                    }
                }
            }

            // Check URL patterns
            if (isset($techData['url'])) {
                foreach ((array) $techData['url'] as $urlPattern) {
                    if (preg_match('/'.preg_quote($urlPattern, '/').'/', $url)) {
                        $detectedTechnologies[$techName] = $techData;
                        break;
                    }
                }
            }

            // Check XHR patterns
            if (isset($techData['xhr'])) {
                foreach ((array) $techData['xhr'] as $xhrPattern) {
                    if (preg_match('/'.preg_quote($xhrPattern, '/').'/', $html)) {
                        $detectedTechnologies[$techName] = $techData;
                        break;
                    }
                }
            }

            // Check Meta tag patterns
            if (isset($techData['meta'])) {
                foreach ($techData['meta'] as $metaName => $metaPattern) {
                    if (preg_match("/<meta[^>]*name=[\"']".preg_quote($metaName, '/')."[\"'][^>]*content=[\"']".preg_quote($metaPattern, '/')."[\"'][^>]*>/i", $html)) {
                        $detectedTechnologies[$techName] = $techData;
                        break;
                    }
                }
            }

            // Check Script Source patterns
            if (isset($techData['scriptSrc'])) {
                foreach ((array) $techData['scriptSrc'] as $scriptPattern) {
                    if (preg_match('/'.preg_quote($scriptPattern, '/').'/', $html)) {
                        $detectedTechnologies[$techName] = $techData;
                        break;
                    }
                }
            }

            // Check DNS patterns (MX, TXT, etc.)
            if (isset($techData['dns'])) {
                foreach ($techData['dns'] as $recordType => $dnsPatterns) {
                    $dnsRecords = @dns_get_record($url, constant('DNS_'.strtoupper($recordType)));
                    foreach ($dnsPatterns as $dnsPattern) {
                        foreach ($dnsRecords as $dnsRecord) {
                            if (isset($dnsRecord['target']) && preg_match('/'.preg_quote($dnsPattern, '/').'/', $dnsRecord['target'])) {
                                $detectedTechnologies[$techName] = $techData;
                                break 3;
                            }
                        }
                    }
                }
            }

            // Check Robots.txt patterns
            if (isset($techData['robots'])) {
                foreach ((array) $techData['robots'] as $robotsPattern) {
                    if (preg_match('/'.preg_quote($robotsPattern, '/').'/', $robotsTxt)) {
                        $detectedTechnologies[$techName] = $techData;
                        break;
                    }
                }
            }

            // Check Script content patterns
            if (isset($techData['scripts'])) {
                foreach ((array) $techData['scripts'] as $scriptContentPattern) {
                    if (preg_match('/'.preg_quote($scriptContentPattern, '/').'/', $html)) {
                        $detectedTechnologies[$techName] = $techData;
                        break;
                    }
                }
            }
        }

        return $detectedTechnologies;
    }
}
