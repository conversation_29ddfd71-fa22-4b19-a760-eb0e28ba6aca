<?php

namespace App\Helpers;

use App\Models\Company;
use App\Models\Lead;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CompanyHelper // Renamed from VatHelper
{
    /**
     * Maximum number of API calls per day
     */
    const MAX_DAILY_CALLS = 50; // Specific to company registration API

    /**
     * HTTP request timeout in seconds
     */
    const REQUEST_TIMEOUT = 30;

    /**
     * Fetch and update Company information for a lead using company registration API.
     *
     * @param  Lead  $lead  The lead model instance.
     *
     * @throws \Exception When API calls fail or other errors occur.
     */
    public static function fetchAndUpdateCompanyInfo(Lead $lead): void // Renamed method
    {
        // Get the domain owner from the lead
        $rawDomainOwner = $lead->domain_owner;

        // First, check if the raw domain owner is empty
        if (empty($rawDomainOwner)) {
            Log::warning('[CompanyHelper] Domain owner is empty for lead ID: '.$lead->id.'. No CVR lookup will be performed.');

            return;
        }
        // Save the initial state of searchTerm for logging, before it's potentially cleared
        $originalSearchTermForLog = $rawDomainOwner;

        // Initialize searchTerm with the lowercased domain owner
        $searchTerm = mb_strtolower($rawDomainOwner);

        // Define suffixes that indicate the term should be cleared if it ends with them.
        // Note the leading space to match " Company ApS" but not "CompanyApS".
        $suffixesToClear = [' aps', ' a/s'];

        // Check if the searchTerm ends with one of the defined suffixes
        // If a match is found, log, clear searchTerm, and exit the loop.
        foreach ($suffixesToClear as $suffix) {
            if (str_ends_with($searchTerm, $suffix)) {
                Log::info('[CompanyHelper] Search term "'.$originalSearchTermForLog.'" for lead ID: '.$lead->id.' matches company type suffix "'.trim($suffix).'". Clearing search term for CVR lookup.');
                $searchTerm = '';
                break; // Suffix matched, term cleared, no need to check further.
            }
        }

        // Final check: if searchTerm is now empty (either from an empty domain_owner or after clearing based on suffix)
        if (empty($searchTerm)) {
            Log::warning('[CompanyHelper] Final search term is empty for lead ID: '.$lead->id.'. Original domain owner: "'.$rawDomainOwner.'". No CVR lookup will be performed.');

            return; // Exit if searchTerm is empty, as no lookup can be done
        }

        $cacheKey = 'company_api_calls_'.now()->format('Y-m-d');
        $dailyCount = Cache::get($cacheKey, 0);

        if ($dailyCount >= self::MAX_DAILY_CALLS) {
            Log::warning('[CompanyHelper] Daily company API call limit reached. Skipping lead ID: '.$lead->id);

            return;
        }

        Log::info('[CompanyHelper] Performing company lookup for: '.$searchTerm.' (Lead ID: '.$lead->id.')');

        $newDailyCount = $dailyCount + 1;
        Cache::put($cacheKey, $newDailyCount, now()->endOfDay());

        try {
            $response = Http::timeout(self::REQUEST_TIMEOUT)
                ->retry(2, 1000) // Retry twice, wait 1 second between retries
                ->get('https://cvrapi.dk/api', [
                    'search' => $searchTerm,
                    'country' => 'dk',
                ]);

            if ($response->successful()) {
                $data = $response->json();

                if ($data && isset($data['vat']) && ! isset($data['error'])) {
                    $vatNumber = (string) $data['vat'];

                    // Prepare data for the Company model
                    $companyData = [
                        'name' => $data['name'] ?? null,
                        'address' => $data['address'] ?? null,
                        'zipcode' => isset($data['zipcode']) ? (string) $data['zipcode'] : null,
                        'city' => $data['city'] ?? null,
                        'protected' => $data['protected'] ?? false,
                        'phone' => isset($data['phone']) ? (string) $data['phone'] : null,
                        'email' => $data['email'] ?? null,
                        'employees' => $data['employees'] ?? null,
                        'industry_code' => $data['industrycode'] ?? null, // Numeric code
                        'company_code' => $data['companycode'] ?? null,   // Numeric code
                        'status' => $data['companydesc'] ?? null, // Text description like "Anpartsselskab"
                        'owners' => ! empty($data['owners']) ? json_encode($data['owners']) : null,
                    ];

                    // Handle date parsing for startdate
                    if (! empty($data['startdate'])) {
                        try {
                            $startDateString = str_replace(' - ', '/', $data['startdate']);
                            $companyData['date_start'] = \Carbon\Carbon::createFromFormat('d/m/Y', $startDateString)->startOfDay();
                        } catch (\Exception $e) {
                            Log::warning("[CompanyHelper] Could not parse startdate '{$data['startdate']}' for CVR {$vatNumber}. Error: {$e->getMessage()}");
                            $companyData['date_start'] = null;
                        }
                    } else {
                        $companyData['date_start'] = null;
                    }

                    // Handle date parsing for enddate
                    if (! empty($data['enddate'])) {
                        try {
                            $endDateString = str_replace(' - ', '/', $data['enddate']);
                            $companyData['date_end'] = \Carbon\Carbon::createFromFormat('d/m/Y', $endDateString)->startOfDay();
                        } catch (\Exception $e) {
                            Log::warning("[CompanyHelper] Could not parse enddate '{$data['enddate']}' for CVR {$vatNumber}. Error: {$e->getMessage()}");
                            $companyData['date_end'] = null;
                        }
                    } else {
                        $companyData['date_end'] = null;
                    }

                    // Update or create the company record
                    $company = \App\Models\Company::updateOrCreate(
                        ['number' => $vatNumber], // Search by 'number' (CVR/VAT)
                        $companyData             // Data to update or create with
                    );

                    if ($company) {
                        // Associate company with lead
                        $lead->company_id = $company->id;
                        $lead->cvr_company_last_synced_at = now(); // Assumes this field exists on Lead model
                        $lead->save();

                        Log::info("[CompanyHelper] Company data for CVR {$vatNumber} ('{$company->name}') processed. Lead ID: {$lead->id} associated with Company ID: {$company->id}.");
                        if ($company->wasRecentlyCreated) {
                            Log::info("[CompanyHelper] Company CVR {$vatNumber} was newly created.");
                        } elseif ($company->wasChanged()) {
                            Log::info("[CompanyHelper] Company CVR {$vatNumber} was updated. Changes: ".json_encode($company->getChanges()));
                        }
                    } else {
                        Log::error("[CompanyHelper] Failed to update or create company for CVR {$vatNumber}. Lead ID: {$lead->id}");
                    }

                } elseif ($data && isset($data['error'])) {
                    // Handle cases where API returns a 2xx status but an error message in the JSON body
                    $errorMessage = is_array($data['error']) ? json_encode($data['error']) : ($data['error'] ?? 'Unknown API error');
                    $additionalMessage = isset($data['message']) ? ' Message: '.(is_array($data['message']) ? json_encode($data['message']) : $data['message']) : '';
                    Log::error("[CompanyHelper] CVR API returned an application-level error for Lead ID {$lead->id} (search \"{$searchTerm}\"). Error: {$errorMessage}.{$additionalMessage}");

                    return; // Stop processing if API reports an error
                } elseif (empty($data)) {
                    Log::warning("[CompanyHelper] CVR API returned empty or invalid data for Lead ID {$lead->id} (search \"{$searchTerm}\").");

                    return; // Stop processing if data is empty
                } else {
                    // Response was successful (2xx) but key data (like VAT) is missing and no error reported in JSON
                    Log::warning("[CompanyHelper] CVR API response for Lead ID {$lead->id} (search \"{$searchTerm}\") is missing expected data (e.g., VAT). Response: ".substr(json_encode($data), 0, 500));

                    return; // Stop processing if essential data like VAT is missing
                }
            } else { // Handle non-successful HTTP responses (e.g., 4xx, 5xx)
                if ($response->status() === 429) {
                    Log::error('[CompanyHelper] Company API rate limit exceeded (Status 429) for Lead ID: '.$lead->id.'. Search term: "'.$searchTerm.'". Setting count to max.');
                    Cache::put($cacheKey, self::MAX_DAILY_CALLS, now()->endOfDay());
                } else {
                    Log::error('[CompanyHelper] Company API HTTP error for Lead ID: '.$lead->id.' (search "'.$searchTerm.'")'.
                        ' - Status: '.$response->status().
                        ' - Response: '.substr($response->body(), 0, 500));
                }

                return; // Stop processing after any non-successful HTTP response from API
            }
        } catch (\Exception $e) {
            Log::error('[CompanyHelper] Failed to fetch company data for Lead ID: '.$lead->id.'. Error: '.$e->getMessage());

            return;
        }
    }
}
