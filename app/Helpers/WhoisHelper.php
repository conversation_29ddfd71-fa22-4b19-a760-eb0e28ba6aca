<?php

namespace App\Helpers;

use App\Models\Lead;
use Exception;
use Illuminate\Support\Facades\Log;
use Iodev\Whois\Factory as WhoisFactory;

class WhoisHelper
{
    /**
     * Socket timeout in seconds
     */
    const SOCKET_TIMEOUT = 30;

    /**
     * Validate domain format.
     *
     * @param  string  $domain  Domain to validate.
     * @return bool Whether domain is valid.
     */
    public static function isValidDomain(string $domain): bool
    {
        // Basic domain validation regex
        // Checks for valid domain format with at least one dot and valid TLD
        return (bool) preg_match('/^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/i', $domain);
    }

    /**
     * Fetch and update WHOIS information for a lead.
     *
     * @param  Lead  $lead  The lead model instance.
     *
     * @throws Exception When WHOIS operations fail or other critical errors occur.
     */
    public static function fetchAndUpdateWhoisInfo(Lead $lead): void
    {
        if (empty($lead->domain)) {
            Log::error('[WhoisHelper] Domain is empty for lead ID: '.$lead->id);

            return;
        }

        if (! self::isValidDomain($lead->domain)) {
            Log::error('[WhoisHelper] Invalid domain format for lead ID: '.$lead->id.' - Domain: '.$lead->domain);
            $lead->domain_status = 0; // Mark as unavailable or invalid
            $lead->save();

            return;
        }

        Log::info('[WhoisHelper] Performing WHOIS lookup for domain: '.$lead->domain.' (Lead ID: '.$lead->id.')');

        $originalTimeout = ini_get('default_socket_timeout');
        try {
            ini_set('default_socket_timeout', self::SOCKET_TIMEOUT);

            $whois = WhoisFactory::get()->createWhois();
            $info = $whois->loadDomainInfo($lead->domain);

            if ($info) {
                $lead->domain_status = 1; // Domain is available/active
                $lead->domain_created = $info->creationDate ? date('Y-m-d H:i:s', $info->creationDate) : null;
                $lead->domain_expires = $info->expirationDate ? date('Y-m-d H:i:s', $info->expirationDate) : null;
                $lead->domain_owner = $info->owner ?? 'Unknown Owner';
                $lead->save();
                Log::info('[WhoisHelper] Successfully updated WHOIS information for domain: '.$lead->domain);
            } else {
                $lead->domain_status = 0; // Domain not found or not available
                $lead->save();
                Log::warning('[WhoisHelper] Domain not found in WHOIS for lead ID: '.$lead->id.' - Domain: '.$lead->domain);
            }
        } catch (Exception $e) {
            Log::error('[WhoisHelper] WHOIS lookup failed for domain: '.$lead->domain.' with error: '.$e->getMessage());
            $lead->domain_status = 0; // Mark as unavailable due to error
            $lead->save();
            throw $e; // Re-throw to be caught by the job's error handling for retries
        } finally {
            ini_set('default_socket_timeout', $originalTimeout);
        }
    }
}
