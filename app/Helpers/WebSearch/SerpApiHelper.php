<?php

namespace App\Helpers\WebSearch;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SerpApiHelper
{
    public const SUPPORTED_COUNTRY_CODES = [
        'AR', // Argentina
        'AU', // Australia
        'AT', // Austria
        'BE', // Belgium
        'BR', // Brazil
        'CA', // Canada
        'CL', // Chile
        'DK', // Denmark
        'FI', // Finland
        'FR', // France
        'DE', // Germany
        'HK', // Hong Kong
        'IN', // India
        'ID', // Indonesia
        'IT', // Italy
        'JP', // Japan
        'KR', // South Korea
        'MY', // Malaysia
        'MX', // Mexico
        'NL', // Netherlands
        'NZ', // New Zealand
        'NO', // Norway
        'CN', // China
        'PL', // Poland
        'PT', // Portugal
        'PH', // Philippines
        'RU', // Russia
        'SA', // Saudi Arabia
        'ZA', // South Africa
        'ES', // Spain
        'SE', // Sweden
        'CH', // Switzerland
        'TW', // Taiwan
        'TR', // Turkey
        'GB', // United Kingdom
        'US', // United States
    ];

    protected string $apiKey;

    protected string $baseUrl = 'https://serpapi.com';

    /**
     * SerpApiHelper constructor.
     *
     * @param  string  $apiKey  Your SerpApi API key.
     *
     * @throws Exception If the API key is not provided.
     */
    public function __construct(string $apiKey)
    {
        if (empty($apiKey)) {
            Log::error('[SerpApiHelper] API key is required but not provided.');
            throw new Exception('SerpApi API key is required.');
        }
        $this->apiKey = $apiKey;
    }

    /**
     * Performs a search via SerpApi.
     *
     * @param  string  $engine  The search engine to use (e.g., 'google', 'bing').
     * @param  string  $query  The search query.
     * @param  string  $countryCode  The country code for the search (e.g., 'US', 'GB'). Defaults to 'US'.
     * @param  array  $additionalParams  Additional parameters to pass to the API.
     * @return array|null The API response as an array, or null on failure.
     */
    public function search(string $engine, string $query, string $countryCode = 'US', array $additionalParams = []): ?array
    {
        if (empty($engine)) {
            Log::error('[SerpApiHelper] Search engine parameter is required.');

            return null;
        }

        if (! in_array(strtoupper($countryCode), self::SUPPORTED_COUNTRY_CODES)) {
            Log::warning('[SerpApiHelper] Provided country code is not in the predefined list of supported codes. Proceeding with the request.', [
                'engine' => $engine,
                'query' => $query,
                'provided_country_code' => $countryCode,
            ]);
        }

        $params = array_merge(
            [
                'engine' => $engine,
                'q' => $query,
                'cc' => $countryCode,
                'api_key' => $this->apiKey,
            ],
            $additionalParams
        );

        try {
            $response = Http::get($this->baseUrl.'/search', $params);

            if ($response->successful()) {
                return $response->json();
            }

            $errorMessage = 'API request failed.';
            $responseBody = $response->body();
            $errorDetails = $response->json();

            if ($errorDetails && isset($errorDetails['error'])) {
                $errorMessage .= ' API Error: '.$errorDetails['error'];
            }

            Log::error('[SerpApiHelper] '.$errorMessage, [
                'engine' => $engine,
                'status' => $response->status(),
                'response_body' => $responseBody,
                'query_params' => array_diff_key($params, ['api_key' => '']),
            ]);

            return null;
        } catch (Exception $e) {
            Log::error('[SerpApiHelper] Exception during API request.', [
                'engine' => $engine,
                'message' => $e->getMessage(),
                'query' => $query,
            ]);

            return null;
        }
    }

    /**
     * Retrieves the organic search results from a search.
     *
     * @param  string  $engine  The search engine to use.
     * @param  string  $query  The search query.
     * @param  string  $countryCode  The country code for the search.
     * @param  array  $additionalParams  Additional parameters.
     * @return array|null Array of organic results or null if not found/on error.
     */
    public function getOrganicResults(string $engine, string $query, string $countryCode = 'US', array $additionalParams = []): ?array
    {
        $response = $this->search($engine, $query, $countryCode, $additionalParams);

        return $response['organic_results'] ?? null;
    }

    /**
     * Retrieves the ad results from a search.
     *
     * @param  string  $engine  The search engine to use.
     * @param  string  $query  The search query.
     * @param  string  $countryCode  The country code for the search.
     * @param  array  $additionalParams  Additional parameters.
     * @return array|null Array of ad results or null if not found/on error.
     */
    public function getAds(string $engine, string $query, string $countryCode = 'US', array $additionalParams = []): ?array
    {
        $response = $this->search($engine, $query, $countryCode, $additionalParams);

        return $response['ads'] ?? null;
    }
}
