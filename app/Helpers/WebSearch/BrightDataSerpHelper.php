<?php

namespace App\Helpers\WebSearch;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BrightDataSerpHelper
{
    /**
     * List of supported country codes for both Google and Bing.
     * These are two-letter country codes used in the 'gl' parameter for Google
     * and 'cc' parameter for Bing.
     */
    public const SUPPORTED_COUNTRY_CODES = [
        'AR', // Argentina
        'AU', // Australia
        'AT', // Austria
        'BE', // Belgium
        'BR', // Brazil
        'CA', // Canada
        'CL', // Chile
        'DK', // Denmark
        'FI', // Finland
        'FR', // France
        'DE', // Germany
        'HK', // Hong Kong
        'IN', // India
        'ID', // Indonesia
        'IT', // Italy
        'JP', // Japan
        'KR', // South Korea
        'MY', // Malaysia
        'MX', // Mexico
        'NL', // Netherlands
        'NZ', // New Zealand
        'NO', // Norway
        'CN', // China
        'PL', // Poland
        'PT', // Portugal
        'PH', // Philippines
        'RU', // Russia
        'SA', // Saudi Arabia
        'ZA', // South Africa
        'ES', // Spain
        'SE', // Sweden
        'CH', // Switzerland
        'TW', // Taiwan
        'TR', // Turkey
        'GB', // United Kingdom
        'US', // United States
    ];

    /**
     * Bright Data API credentials
     */
    protected string $apiKey;
    protected string $customerId;
    protected string $zoneId;

    /**
     * Base URLs for the Bright Data SERP API
     */
    protected string $googleBaseUrl = 'https://api.brightdata.com/serp/google/search';
    protected string $bingBaseUrl = 'https://api.brightdata.com/serp/bing/search';
    protected string $resultUrl = 'https://api.brightdata.com/serp/get_result';

    /**
     * Google domain mapping for country codes
     * Maps country codes to their respective Google domains
     */
    protected array $googleDomains = [
        'us' => 'google.com',
        'uk' => 'google.co.uk',
        'ca' => 'google.ca',
        'au' => 'google.com.au',
        'nz' => 'google.co.nz',
        'ie' => 'google.ie',
        'in' => 'google.co.in',
        'za' => 'google.co.za',
        'br' => 'google.com.br',
        'mx' => 'google.com.mx',
        'ar' => 'google.com.ar',
        'cl' => 'google.cl',
        'co' => 'google.com.co',
        'pe' => 'google.com.pe',
        'jp' => 'google.co.jp',
        'kr' => 'google.co.kr',
        'hk' => 'google.com.hk',
        'sg' => 'google.com.sg',
        'my' => 'google.com.my',
        'ph' => 'google.com.ph',
        'id' => 'google.co.id',
        'th' => 'google.co.th',
        'vn' => 'google.com.vn',
        'de' => 'google.de',
        'fr' => 'google.fr',
        'it' => 'google.it',
        'es' => 'google.es',
        'pt' => 'google.pt',
        'nl' => 'google.nl',
        'be' => 'google.be',
        'ch' => 'google.ch',
        'at' => 'google.at',
        'dk' => 'google.dk',
        'no' => 'google.no',
        'se' => 'google.se',
        'fi' => 'google.fi',
        'pl' => 'google.pl',
        'ru' => 'google.ru',
        'tr' => 'google.com.tr',
        'gr' => 'google.gr',
        'il' => 'google.co.il',
        'ae' => 'google.ae',
        'sa' => 'google.com.sa',
    ];

    /**
     * BrightDataSerpHelper constructor.
     *
     * @param string $apiKey Your Bright Data API key
     * @param string $customerId Your Bright Data customer ID (e.g., 'hl_a84e30e2')
     * @param string $zoneId Your Bright Data zone ID (e.g., 'serp_api1')
     *
     * @throws Exception If any required credentials are not provided
     */
    public function __construct(string $apiKey, string $customerId, string $zoneId)
    {
        if (empty($apiKey)) {
            Log::error('[BrightDataSerpHelper] API key is required but not provided.');
            throw new Exception('Bright Data API key is required.');
        }

        if (empty($customerId)) {
            Log::error('[BrightDataSerpHelper] Customer ID is required but not provided.');
            throw new Exception('Bright Data customer ID is required.');
        }

        if (empty($zoneId)) {
            Log::error('[BrightDataSerpHelper] Zone ID is required but not provided.');
            throw new Exception('Bright Data zone ID is required.');
        }

        $this->apiKey = $apiKey;
        $this->customerId = $customerId;
        $this->zoneId = $zoneId;
    }

    /**
     * Performs a search using the Bright Data SERP API.
     *
     * @param string $engine The search engine to use ('google' or 'bing')
     * @param string $query The search query
     * @param string $countryCode The country code for the search (e.g., 'US', 'GB')
     * @param array $additionalParams Additional parameters for the API request
     *                               Common parameters for both engines:
     *                               - 'brd_mobile': Device type (0 for desktop, 1 for mobile, or specific values like 'ios', 'android')
     *
     *                               Google-specific parameters:
     *                               - 'num': Number of results to return (default: 100)
     *                               - 'tbs': Time-based search (default: 'qdr:d' for current day)
     *                               - 'hl': Language code (default: 'en')
     *                               - 'start': Pagination offset
     *                               - 'tbm': Search type ('isch' for images, 'shop' for shopping, 'nws' for news)
     *                               - 'uule': Geo-location parameter
     *
     *                               Bing-specific parameters:
     *                               - 'count': Number of results to return (default: 100)
     *                               - 'time': Time filter (default: 'day')
     *                               - 'first': Pagination offset
     *                               - 'mkt': Market (e.g., 'en-US')
     *                               - 'location': Location name (requires 'lat' and 'lon')
     *                               - 'lat': Latitude for geo-location
     *                               - 'lon': Longitude for geo-location
     * @return array|null The API response as an array, or null on failure
     */
    public function search(string $engine, string $query, string $countryCode = 'US', array $additionalParams = []): ?array
    {
        if (empty($query)) {
            Log::error('[BrightDataSerpHelper] Search query cannot be empty.');
            return null;
        }

        if (!in_array(strtolower($engine), ['google', 'bing'])) {
            Log::error('[BrightDataSerpHelper] Unsupported search engine. Only "google" and "bing" are supported.');
            return null;
        }

        if (!in_array(strtoupper($countryCode), self::SUPPORTED_COUNTRY_CODES)) {
            Log::warning('[BrightDataSerpHelper] Provided country code is not in the predefined list of supported codes. Proceeding with the request.', [
                'query' => $query,
                'provided_country_code' => $countryCode,
            ]);
        }

        // Prepare the request parameters
        $params = $this->prepareRequestParams($engine, $query, $countryCode, $additionalParams);

        try {
            // Determine the base URL based on the engine
            $baseUrl = strtolower($engine) === 'google' ? $this->googleBaseUrl : $this->bingBaseUrl;

            // Make the initial request to get the response ID
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->post($baseUrl, $params);

            if (!$response->successful()) {
                $this->logError($engine, $response, $query, $params);
                return null;
            }

            // Get the response ID from the headers
            $responseId = $response->header('x-response-id');
            if (empty($responseId)) {
                Log::error('[BrightDataSerpHelper] No response ID returned from Bright Data API.', [
                    'engine' => $engine,
                    'query' => $query,
                ]);
                return null;
            }

            // Get the result using the response ID
            $result = $this->getResult($responseId);

            if ($result && isset($result['error'])) {
                Log::error('[BrightDataSerpHelper] Error in Bright Data API result.', [
                    'engine' => $engine,
                    'query' => $query,
                    'error' => $result['error'],
                ]);
                return null;
            }

            return $result;
        } catch (Exception $e) {
            Log::error('[BrightDataSerpHelper] Exception during API request.', [
                'engine' => $engine,
                'message' => $e->getMessage(),
                'query' => $query,
            ]);
            return null;
        }
    }

    /**
     * Prepares the request parameters based on the search engine.
     *
     * @param string $engine The search engine to use
     * @param string $query The search query
     * @param string $countryCode The country code
     * @param array $additionalParams Additional parameters
     * @return array The prepared parameters
     */
    protected function prepareRequestParams(string $engine, string $query, string $countryCode, array $additionalParams): array
    {
        $countryCode = strtolower($countryCode);
        $engine = strtolower($engine);

        // Common parameters for both engines
        $params = [
            'customer' => $this->customerId,
            'zone' => $this->zoneId,
            'brd_json' => 1, // Return parsed JSON
            'country' => $countryCode, // Country for the request
        ];

        // Device type (common for both engines)
        if (isset($additionalParams['brd_mobile'])) {
            $params['brd_mobile'] = $additionalParams['brd_mobile'];
        }

        // Base query parameters
        $queryParams = ['q' => $query];

        // Add engine-specific parameters
        if ($engine === 'google') {
            $queryParams = array_merge($queryParams, $this->getGoogleParams($countryCode, $additionalParams));

            // Add Google domain based on country code
            $domain = $this->getGoogleDomain($countryCode);
            if (!empty($domain)) {
                $params['domain'] = $domain;
            }
        } else { // bing
            $queryParams = array_merge($queryParams, $this->getBingParams($countryCode, $additionalParams));
        }

        // Add the query parameters to the main params
        $params['query'] = $queryParams;

        return $params;
    }

    /**
     * Get Google-specific search parameters.
     *
     * @param string $countryCode The country code
     * @param array $additionalParams Additional parameters
     * @return array The Google search parameters
     */
    protected function getGoogleParams(string $countryCode, array $additionalParams): array
    {
        $countryCode = strtolower($countryCode);

        $params = [
            'gl' => $countryCode, // Country code for Google (e.g., 'dk', 'us')
            'hl' => $additionalParams['hl'] ?? 'en', // Language code (default to English)
            'num' => $additionalParams['num'] ?? 100, // Number of results
            'tbs' => $additionalParams['tbs'] ?? 'qdr:d', // Time-based search (current day)
        ];

        // Add pagination parameters if provided
        if (isset($additionalParams['start'])) {
            $params['start'] = $additionalParams['start'];
        }

        // Add search type if provided
        if (isset($additionalParams['tbm'])) {
            $params['tbm'] = $additionalParams['tbm'];
        }

        // Add geo-location if provided
        if (isset($additionalParams['uule'])) {
            $params['uule'] = $additionalParams['uule'];
        }

        // Set the domain based on country code (e.g., google.dk, google.com)
        // This is done by modifying the base URL in the search method

        return $params;
    }

    /**
     * Get Bing-specific search parameters.
     *
     * @param string $countryCode The country code
     * @param array $additionalParams Additional parameters
     * @return array The Bing search parameters
     */
    protected function getBingParams(string $countryCode, array $additionalParams): array
    {
        $params = [
            'cc' => strtolower($countryCode), // Country code for Bing
            'count' => $additionalParams['count'] ?? 100, // Number of results
            'time' => $additionalParams['time'] ?? 'day', // Time-based search (current day)
        ];

        // Add pagination parameters if provided
        if (isset($additionalParams['first'])) {
            $params['first'] = $additionalParams['first'];
        }

        // Add market parameter if provided
        if (isset($additionalParams['mkt'])) {
            $params['mkt'] = $additionalParams['mkt'];
        }

        // Add geo-location parameters if provided
        if (isset($additionalParams['location'])) {
            $params['location'] = $additionalParams['location'];

            // Location requires lat and lon
            if (isset($additionalParams['lat']) && isset($additionalParams['lon'])) {
                $params['lat'] = $additionalParams['lat'];
                $params['lon'] = $additionalParams['lon'];
            }
        }

        return $params;
    }

    /**
     * Gets the result using the response ID.
     *
     * @param string $responseId The response ID from the initial request
     * @return array|null The result or null on failure
     */
    protected function getResult(string $responseId): ?array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->resultUrl, [
                'customer' => $this->customerId,
                'zone' => $this->zoneId,
                'response_id' => $responseId,
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('[BrightDataSerpHelper] Failed to get result from Bright Data API.', [
                'response_id' => $responseId,
                'status' => $response->status(),
                'body' => $response->body(),
            ]);

            return null;
        } catch (Exception $e) {
            Log::error('[BrightDataSerpHelper] Exception while getting result.', [
                'response_id' => $responseId,
                'message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Logs an error from the API response.
     *
     * @param string $engine The search engine used
     * @param \Illuminate\Http\Client\Response $response The HTTP response
     * @param string $query The search query
     * @param array $params The request parameters
     */
    protected function logError(string $engine, $response, string $query, array $params): void
    {
        $errorMessage = 'Bright Data API request failed.';
        $responseBody = $response->body();
        $errorDetails = $response->json();

        if ($errorDetails && isset($errorDetails['error'])) {
            $errorMessage .= ' API Error: ' . $errorDetails['error'];
        }

        Log::error('[BrightDataSerpHelper] ' . $errorMessage, [
            'engine' => $engine,
            'status' => $response->status(),
            'query' => $query,
            'params' => array_diff_key($params, ['api_key' => '']),
            'response_body' => $responseBody,
        ]);
    }

    /**
     * Get the Google domain for a specific country code.
     *
     * @param string $countryCode The country code
     * @return string|null The Google domain or null if not found
     */
    protected function getGoogleDomain(string $countryCode): ?string
    {
        $countryCode = strtolower($countryCode);

        // Return the domain from the mapping if it exists
        if (isset($this->googleDomains[$countryCode])) {
            return $this->googleDomains[$countryCode];
        }

        // Default to google.com for unknown country codes
        return 'google.com';
    }
}
