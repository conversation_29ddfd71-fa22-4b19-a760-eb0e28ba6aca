<?php

namespace App\Helpers\WebSearch;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SerpStackHelper
{
    public const SUPPORTED_COUNTRY_CODES = [
        'AR', // Argentina
        'AU', // Australia
        'AT', // Austria
        'BE', // Belgium
        'BR', // Brazil
        'CA', // Canada
        'CL', // Chile
        'DK', // Denmark
        'FI', // Finland
        'FR', // France
        'DE', // Germany
        'HK', // Hong Kong
        'IN', // India
        'ID', // Indonesia
        'IT', // Italy
        'JP', // Japan
        'KR', // South Korea
        'MY', // Malaysia
        'MX', // Mexico
        'NL', // Netherlands
        'NZ', // New Zealand
        'NO', // Norway
        'CN', // China
        'PL', // Poland
        'PT', // Portugal
        'PH', // Philippines
        'RU', // Russia
        'SA', // Saudi Arabia
        'ZA', // South Africa
        'ES', // Spain
        'SE', // Sweden
        'CH', // Switzerland
        'TW', // Taiwan
        'TR', // Turkey
        'GB', // United Kingdom
        'US', // United States
    ];

    protected string $apiKey;

    protected string $baseUrl;

    /**
     * SerpStackHelper constructor.
     *
     * @param  string  $apiKey  Your SerpStack API access key.
     * @param  bool  $useHttps  Whether to use HTTPS (requires Basic Plan or higher).
     *
     * @throws Exception If the API key is not provided.
     */
    public function __construct(string $apiKey, bool $useHttps = false)
    {
        if (empty($apiKey)) {
            Log::error('[SerpStackHelper] API key is required but not provided.');
            throw new Exception('SerpStack API key is required.');
        }
        $this->apiKey = $apiKey;
        $protocol = $useHttps ? 'https' : 'http';
        $this->baseUrl = $protocol.'://api.serpstack.com/search';
    }

    /**
     * Performs a search using the SerpStack API.
     *
     * @param  string  $query  The search query.
     * @param  array  $additionalParams  Additional parameters for the API request.
     *                                   Refer to SerpStack documentation for all available parameters (e.g., type, device, location, gl, hl, page, num).
     * @return array|null The API response as an array, or null on failure.
     */
    public function search(string $query, array $additionalParams = []): ?array
    {
        if (empty($query)) {
            Log::error('[SerpStackHelper] Search query cannot be empty.');

            return null;
        }

        if (isset($additionalParams['gl']) &&
            ! empty($additionalParams['gl']) &&
            ! in_array(strtoupper($additionalParams['gl']), self::SUPPORTED_COUNTRY_CODES)) {
            Log::warning('[SerpStackHelper] Provided country code (gl) is not in the predefined list. Proceeding with the request.', [
                'query' => $query,
                'provided_gl_code' => $additionalParams['gl'],
            ]);
        }

        $defaultParams = [
            'access_key' => $this->apiKey,
            'query' => $query,
            'output' => 'json',
        ];
        $params = array_merge($defaultParams, $additionalParams);

        try {
            $response = Http::get($this->baseUrl, $params);
            $responseData = $response->json();

            if ($response->successful() && isset($responseData['request']['success']) && $responseData['request']['success'] === true) {
                return $responseData;
            }

            if (isset($responseData['success']) && $responseData['success'] === false && isset($responseData['error'])) {
                $error = $responseData['error'];
                $errorMessage = 'SerpStack API Error: Code '.($error['code'] ?? 'N/A').' - Type: '.($error['type'] ?? 'N/A').' - Info: '.($error['info'] ?? 'No additional info.');
                Log::error('[SerpStackHelper] '.$errorMessage, [
                    'status' => $response->status(),
                    'query_params' => array_diff_key($params, ['access_key' => '']),
                    'error_details' => $error,
                ]);

                return null;
            }

            // General failure if not caught by SerpStack's success flag
            Log::error('[SerpStackHelper] API request failed or returned an unexpected format.', [
                'status' => $response->status(),
                'query_params' => array_diff_key($params, ['access_key' => '']),
                'response_body' => $response->body(), // Full body for debugging
            ]);

            return null;
        } catch (Exception $e) {
            Log::error('[SerpStackHelper] Exception during API request.', [
                'message' => $e->getMessage(),
                'query' => $query,
            ]);

            return null;
        }
    }

    public function getRequestInfo(array $apiResponse): ?array
    {
        return $apiResponse['request'] ?? null;
    }

    public function getSearchParameters(array $apiResponse): ?array
    {
        return $apiResponse['search_parameters'] ?? null;
    }

    public function getSearchInformation(array $apiResponse): ?array
    {
        return $apiResponse['search_information'] ?? null;
    }

    public function getOrganicResults(array $apiResponse): ?array
    {
        return $apiResponse['organic_results'] ?? null;
    }

    public function getAds(array $apiResponse): ?array
    {
        return $apiResponse['ads'] ?? null;
    }

    public function getLocalResults(array $apiResponse): ?array
    {
        return $apiResponse['local_results'] ?? null;
    }

    public function getKnowledgeGraph(array $apiResponse): ?array
    {
        return $apiResponse['knowledge_graph'] ?? null;
    }

    public function getRelatedSearches(array $apiResponse): ?array
    {
        return $apiResponse['related_searches'] ?? null;
    }

    public function getRelatedQuestions(array $apiResponse): ?array
    {
        return $apiResponse['related_questions'] ?? null;
    }

    public function getPaginationInfo(array $apiResponse): ?array
    {
        return $apiResponse['pagination'] ?? null;
    }
}
