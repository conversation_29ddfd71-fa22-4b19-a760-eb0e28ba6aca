<?php

namespace App\Helpers;

use Exception;
use Guz<PERSON>Http\Client as HttpClient;
use GuzzleHttp\Psr7\HttpFactory;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Pdp\CannotProcessHost;
use Pdp\Storage\PsrStorageFactory;
use Pdp\SyntaxError as PdpSyntaxError;

// General exception

class DomainParserHelper
{
    /**
     * Normalizes a raw URL or domain string, extracts the hostname,
     * resolves it using IANA TLD list, and returns comprehensive domain information.
     *
     * This function combines the purpose of normalizing input, extracting the
     * registrable domain, the TLD (public suffix based on IANA list),
     * and constructing a standard website URL.
     *
     * @param  string  $rawUrlOrDomain  The raw input string, which can be a full URL or just a domain.
     * @return array|null An associative array ['domain' => string, 'tld' => string] or null on failure.
     *                    'domain': The registrable domain (e.g., "example.com").
     *                    'tld': The Top-Level Domain (e.g., "com").
     */
    public static function getProcessedDomainInformation(string $rawUrlOrDomain): ?array
    {
        if (empty(trim($rawUrlOrDomain))) {
            Log::info('[DomainParserHelper::getProcessedDomainInformation] Input was empty or whitespace.');

            return null;
        }

        $processedInput = strtolower(trim($rawUrlOrDomain));

        // Add a scheme if missing to aid parse_url.
        if (! preg_match('#^https?://#i', $processedInput) && ! preg_match('#^//#i', $processedInput)) {
            $processedInput = '//'.$processedInput;
        }

        $parsedUrl = parse_url($processedInput);
        $hostname = null; // Variable to store the extracted hostname

        if ($parsedUrl && ! empty($parsedUrl['host'])) {
            $hostname = $parsedUrl['host'];
        } elseif ($parsedUrl && empty($parsedUrl['host']) && ! empty($parsedUrl['path'])) {
            // If no host, path might be a domain (e.g., "example.com" input)
            $pathAsDomainCandidate = $parsedUrl['path'];
            // Ensure it has a dot and isn't a deeper path
            if (strpos($pathAsDomainCandidate, '.') !== false && ! str_contains(trim($pathAsDomainCandidate, '/'), '/')) {
                $hostname = trim($pathAsDomainCandidate, '/');
            }
        }

        // If hostname still not found, and original input has a dot and no slashes, treat original as hostname
        if ($hostname === null) {
            $originalCleaned = strtolower(trim($rawUrlOrDomain));
            if (strpos($originalCleaned, '.') !== false && ! str_contains($originalCleaned, '/')) {
                $hostname = $originalCleaned;
            } else {
                Log::info("[DomainParserHelper::getProcessedDomainInformation] Could not determine hostname from input: {$rawUrlOrDomain}");

                return null;
            }
        }

        if (empty($hostname) || strlen($hostname) > 255) {
            Log::info("[DomainParserHelper::getProcessedDomainInformation] Extracted hostname '{$hostname}' is invalid (empty or too long) from input: {$rawUrlOrDomain}");

            return null;
        }

        // Use Pdp\TopLevelDomains to get the registrable domain and public suffix
        try {
            $cache = Cache::store();
            $httpClient = new HttpClient;
            $requestFactory = new HttpFactory;
            $storageFactory = new PsrStorageFactory($cache, $httpClient, $requestFactory);

            // Create storage for Top Level Domains list.
            // Using a distinct cache key for TLD data, e.g., 'pdp_tld_v1_'.
            $tldStorage = $storageFactory->createTopLevelDomainListStorage('pdp_tld_v1_', new \DateInterval('P1D'));
            /** @var \Pdp\TopLevelDomains $topLevelDomainsList */
            $topLevelDomainsList = $tldStorage->get(PsrStorageFactory::TOP_LEVEL_DOMAIN_LIST_URI);

            $resolvedDomainPdp = $topLevelDomainsList->resolve($hostname);

            $registrableDomainObj = $resolvedDomainPdp->registrableDomain();
            $publicSuffixObj = $resolvedDomainPdp->suffix();

            if ($registrableDomainObj === null || $registrableDomainObj->value() === null) {
                Log::info("[DomainParserHelper::getProcessedDomainInformation] PDP TopLevelDomains: Could not determine registrable domain for '{$hostname}' (from input '{$rawUrlOrDomain}').");

                return null;
            }

            $finalRegistrableDomain = $registrableDomainObj->toString();
            $finalPublicSuffix = ($publicSuffixObj && $publicSuffixObj->value() !== null) ? $publicSuffixObj->toString() : null;

            if (empty($finalRegistrableDomain)) {
                Log::info("[DomainParserHelper::getProcessedDomainInformation] PDP TopLevelDomains: Registrable domain resolved to an empty string for '{$hostname}'.");

                return null;
            }
            // If $finalPublicSuffix is null, it means the suffix part was not identifiable from the TLD list.

        } catch (CannotProcessHost|PdpSyntaxError $e) {
            Log::info("[DomainParserHelper::getProcessedDomainInformation] PDP TopLevelDomains: Cannot process hostname '{$hostname}' (from input '{$rawUrlOrDomain}'): ".$e->getMessage());

            return null;
        } catch (Exception $e) {
            Log::error("[DomainParserHelper::getProcessedDomainInformation] Failed to initialize or use PDP TopLevelDomains components for '{$hostname}': ".$e->getMessage());

            return null;
        }

        // Return the registrable domain (e.g., "example.com") and its public suffix (e.g., "com")
        return [
            'domain' => $finalRegistrableDomain,
            'tld' => $finalPublicSuffix,
        ];
    }
}
