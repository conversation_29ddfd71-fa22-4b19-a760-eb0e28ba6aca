<?php

declare(strict_types=1);

namespace App\Helpers;

use App\Models\Lead;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GooglePageSpeedHelper
{
    /**
     * Maximum number of API calls per day
     * Free tier of PageSpeed API typically allows 1,000 queries per day
     * Setting a conservative limit to avoid hitting the quota
     */
    const MAX_DAILY_CALLS = 900;

    /**
     * HTTP request timeout in seconds
     */
    const REQUEST_TIMEOUT = 90;

    /**
     * Update Lead PageSpeed Data.
     *
     * @param  Lead  $lead  The lead model instance.
     *
     * @throws \Exception When API calls fail to trigger retry mechanism.
     */
    public static function updateLeadPageSpeedData(Lead $lead): void
    {
        // Check if domain is set
        if (empty($lead->domain)) {
            Log::error('Domain is empty for lead ID: '.$lead->id.' in GooglePageSpeedHelper.');

            return;
        }

        // Check if we've reached the daily API call limit
        $cacheKey = 'pagespeed_api_calls_'.now()->format('Y-m-d');
        $dailyCount = Cache::get($cacheKey, 0);

        if ($dailyCount >= self::MAX_DAILY_CALLS) {
            Log::warning('Daily PageSpeed API call limit reached. Skipping lead ID: '.$lead->id.' in GooglePageSpeedHelper.');

            return;
        }

        // Define how to extract data from the API response and map to database fields
        $metricConfigurations = [
            'performance' => [
                'jsonPath' => 'lighthouseResult.categories.performance.score',
                'dbFieldSuffix' => '_performance_score',
                'isScore' => true,
            ],
            'accessibility' => [
                'jsonPath' => 'lighthouseResult.categories.accessibility.score',
                'dbFieldSuffix' => '_accessibility_score',
                'isScore' => true,
            ],
            'seo' => [
                'jsonPath' => 'lighthouseResult.categories.seo.score',
                'dbFieldSuffix' => '_seo_score',
                'isScore' => true,
            ],
            'best-practices' => [
                'jsonPath' => 'lighthouseResult.categories.best-practices.score',
                'dbFieldSuffix' => '_best_practices_score',
                'isScore' => true,
            ],
            'screenshot' => [
                'jsonPath' => 'lighthouseResult.audits.final-screenshot.details.data',
                'dbFieldSuffix' => '_screenshot',
                'isScore' => false,
            ],
        ];

        $dbFieldPrefix = 'google_pagespeed_';
        $strategiesToRun = ['desktop', 'mobile'];

        $hasChanges = false;
        $hasCriticalPageLoadFailure = false; // Flag to track critical failure
        $quotaExceeded = false; // Flag to track quota exceeded error

        // Loop through both desktop and mobile strategies
        foreach ($strategiesToRun as $strategy) {
            // Skip if a previous strategy had a critical page load failure
            if ($hasCriticalPageLoadFailure) {
                Log::info("Skipping PageSpeed strategy '{$strategy}' for lead ID {$lead->id} due to FAILED_DOCUMENT_REQUEST on a previous strategy in GooglePageSpeedHelper.");

                continue;
            }

            // Skip if quota exceeded error occurred
            if ($quotaExceeded) {
                Log::info("Stopping further processing for lead ID {$lead->id} due to quota exceeded error in GooglePageSpeedHelper.");
                break;
            }

            // Check API limit again before each strategy
            if (Cache::get($cacheKey, 0) >= self::MAX_DAILY_CALLS) {
                Log::warning('Daily PageSpeed API call limit reached during processing. Skipping strategy: '.$strategy.' for lead ID: '.$lead->id.' in GooglePageSpeedHelper.');

                continue;
            }

            $apiCallDetails = self::fetchPageSpeedData($lead->website_url, $strategy, $cacheKey);

            if (! empty($apiCallDetails['response_json'])) {
                $result = $apiCallDetails['response_json'];

                foreach ($metricConfigurations as $metricConfig) {
                    $value = data_get($result, $metricConfig['jsonPath']);

                    if ($value !== null) {
                        $leadField = $dbFieldPrefix.$strategy.$metricConfig['dbFieldSuffix'];
                        $lead->{$leadField} = $metricConfig['isScore'] ? $value * 100 : $value;
                        $hasChanges = true;
                    } else {
                        // Log missing jsonPath
                        Log::warning("Missing jsonPath: {$metricConfig['jsonPath']} for lead ID: {$lead->id}, domainUrl: {$lead->domain}, strategy: {$strategy} in GooglePageSpeedHelper.");
                    }
                }
            } else {
                // API call failed or an exception occurred. Error is already logged by fetchPageSpeedData.
                // Check for critical FAILED_DOCUMENT_REQUEST error
                if (($apiCallDetails['http_status'] ?? null) == 400) {
                    $responseBody = $apiCallDetails['response_raw_body'] ?? '';
                    if (str_contains($responseBody, 'FAILED_DOCUMENT_REQUEST')) {
                        Log::warning("PageSpeed FAILED_DOCUMENT_REQUEST for lead ID: {$lead->id}, domainUrl: {$lead->domain}, strategy: {$strategy}. Subsequent strategies will be skipped. (GooglePageSpeedHelper)");
                        $hasCriticalPageLoadFailure = true;
                    }
                } elseif (($apiCallDetails['http_status'] ?? null) == 429) {
                    $quotaExceeded = true; // Flag is set, logged by fetchPageSpeedData
                }
            }
        }

        if ($hasChanges) {
            try {
                $lead->save();
                Log::info('Successfully updated PageSpeed data for lead ID: '.$lead->id.' in GooglePageSpeedHelper.');
            } catch (\Exception $e) {
                Log::error('Failed to save PageSpeed data for lead ID: '.$lead->id.'. Error: '.$e->getMessage().' in GooglePageSpeedHelper.');
            }
        } elseif ($hasCriticalPageLoadFailure) {
            Log::warning('No PageSpeed data saved for lead ID: '.$lead->id.' due to critical FAILED_DOCUMENT_REQUEST. (GooglePageSpeedHelper)');
        } elseif ($quotaExceeded) {
            Log::warning('No PageSpeed data saved for lead ID: '.$lead->id.' due to API quota exceeded. (GooglePageSpeedHelper)');
        } else {
            // This case might occur if all API calls returned empty/failed responses without being critical errors, or no data points were found.
            Log::info('No changes to PageSpeed data for lead ID: '.$lead->id.' (GooglePageSpeedHelper). This might be due to API returning no actionable data or errors not classified as critical.');
        }
    }

    /**
     * Fetch PageSpeed data from Google API
     *
     * @param  string  $domain  Domain to check
     * @param  string  $strategy  Strategy (desktop or mobile)
     * @param  string  $cacheKey  Cache key for API call counting
     * @return array Result data, including 'duration', 'exception', 'http_status', 'response_json', 'response_raw_body'
     */
    public static function fetchPageSpeedData(string $domainUrl, string $strategy, string $cacheKey): array
    {
        $apiKey = config('crawler.google_api_key');
        if (empty($apiKey)) {
            Log::critical('Google API key is not configured. Please set CRAWLER_GOOGLE_API_KEY in your .env file. (GooglePageSpeedHelper)');

            return [
                'duration' => 0,
                'exception' => new Exception('Google API key not configured.'),
                'http_status' => null,
                'response_json' => null,
                'response_raw_body' => null,
            ];
        }

        $apiCallStartTime = microtime(true);
        $response = null;
        $caughtException = null;
        $httpStatus = null;
        $rawBody = null;

        try {
            Log::info('Running PageSpeed test for domainUrl: '.$domainUrl.' with strategy: '.$strategy.' (GooglePageSpeedHelper)');

            // Increment the API call counter before making the request
            $dailyCount = Cache::get($cacheKey, 0) + 1;
            Cache::put($cacheKey, $dailyCount, now()->endOfDay());

            try {
                // Construct the URL string manually
                $categories = [
                    'PERFORMANCE',
                    'ACCESSIBILITY',
                    'BEST_PRACTICES',
                    'SEO',
                ];

                $categoryString = '';
                foreach ($categories as $category) {
                    $categoryString .= '&category='.rawurlencode($category);
                }

                $fullUrl = 'https://www.googleapis.com/pagespeedonline/v5/runPagespeed'.
                           '?url='.rawurlencode($domainUrl).
                           '&strategy='.rawurlencode($strategy).
                           $categoryString. // Categories are already URL encoded and have leading '&'
                           '&key='.rawurlencode($apiKey); // Use the validated $apiKey variable

                $response = Http::timeout(self::REQUEST_TIMEOUT)
                    ->get($fullUrl); // Pass the fully constructed URL
                $httpStatus = $response->status();
                $rawBody = $response->body();
            } catch (Exception $e) {
                $caughtException = $e; // Capture client-specific exceptions (e.g., ConnectionException)
            }

            $apiCallDuration = round(microtime(true) - $apiCallStartTime, 2);

            if ($caughtException) {
                Log::error('Google PageSpeed API client exception for domainUrl: '.$domainUrl.' with strategy: '.$strategy.
                    ' - Error: '.$caughtException->getMessage().
                    ' - Took: '.$apiCallDuration.'s (GooglePageSpeedHelper)');

                return ['duration' => $apiCallDuration, 'exception' => $caughtException->getMessage(), 'http_status' => null, 'response_json' => null, 'response_raw_body' => null];
            }

            if ($response === null) {
                Log::error('Google PageSpeed API call resulted in a null response object for domainUrl: '.$domainUrl.' strategy: '.$strategy.'. Took: '.$apiCallDuration.'s (GooglePageSpeedHelper)');

                return ['duration' => $apiCallDuration, 'exception' => 'Null response object from HTTP client', 'http_status' => null, 'response_json' => null, 'response_raw_body' => null];
            }

            if (! $response->successful()) {
                if ($httpStatus === 429) { // Quota Exceeded
                    Log::error('Google PageSpeed API quota exceeded for domainUrl: '.$domainUrl.' Strategy: '.$strategy.'. (Consider upgrading API plan or reducing daily requests). Took: '.$apiCallDuration.'s (GooglePageSpeedHelper)');
                    Cache::put($cacheKey, self::MAX_DAILY_CALLS, now()->endOfDay());
                } else { // Other non-successful HTTP statuses
                    Log::error('Google PageSpeed API error for domainUrl: '.$domainUrl.' with strategy: '.$strategy.
                        ' - Status: '.$httpStatus.' - Response: '.substr($rawBody, 0, 200).
                        ' - Took: '.$apiCallDuration.'s (GooglePageSpeedHelper)');
                }

                return ['duration' => $apiCallDuration, 'exception' => null, 'http_status' => $httpStatus, 'response_json' => null, 'response_raw_body' => $rawBody];
            }

            $jsonResponse = $response->json();
            if ($jsonResponse === null && ! empty($rawBody) && $httpStatus === 200) {
                Log::warning('Google PageSpeed API response was not valid JSON despite successful HTTP status for domainUrl: '.$domainUrl.' strategy: '.$strategy.' - Status: '.$httpStatus.' Took: '.$apiCallDuration.'s (GooglePageSpeedHelper)');
            }

            Log::info('Google PageSpeed API success for domainUrl: '.$domainUrl.' strategy: '.$strategy.
                ' - Status: '.$httpStatus.' Took: '.$apiCallDuration.'s (GooglePageSpeedHelper)');

            return ['duration' => $apiCallDuration, 'exception' => null, 'http_status' => $httpStatus, 'response_json' => $jsonResponse, 'response_raw_body' => $rawBody];

        } catch (Exception $e) { // Outer catch for any other unexpected issues
            $apiCallDuration = (isset($apiCallStartTime) && $apiCallStartTime) ? round(microtime(true) - $apiCallStartTime, 2) : 'unknown';
            Log::error('General exception in fetchPageSpeedData for domainUrl: '.$domainUrl.' with strategy: '.$strategy.
                ' - Error: '.$e->getMessage().
                ' - Took approx: '.$apiCallDuration.'s (GooglePageSpeedHelper)');

            return ['duration' => $apiCallDuration, 'exception' => $e->getMessage(), 'http_status' => null, 'response_json' => null, 'response_raw_body' => null];
        }
    }
}
