<?php

namespace App\Helpers;

use App\Models\Lead;
use Illuminate\Support\Facades\Log;

class LeadBatchProcessorHelper
{
    private int $batchSize;

    /**
     * Constructor for LeadBatchProcessorHelper.
     *
     * @param  int  $batchSize  The number of leads to process in each batch.
     */
    public function __construct(int $batchSize = 1000)
    {
        $this->batchSize = $batchSize;
    }

    /**
     * Processes a list of prepared lead data arrays, batching them for upsert.
     * Each element in $preparedLeadsData must be an associative array containing:
     * - 'domain' (string): The domain name.
     * - 'website_url' (string): The full website URL.
     * - 'tld' (string|null): The top-level domain.
     *
     * @param  array  $preparedLeadsData  An array of pre-validated lead data structures.
     */
    public function processLeads(array $preparedLeadsData): void
    {
        if (empty($preparedLeadsData)) {
            Log::info('[LeadBatchProcessorHelper] No leads provided to processLeads method.');

            return;
        }

        // Chunk the prepared leads and process each chunk
        $leadChunks = array_chunk($preparedLeadsData, $this->batchSize);

        foreach ($leadChunks as $chunk) {
            // This check is mostly a safeguard; array_chunk with non-empty $preparedLeadsData should produce non-empty chunks unless $this->batchSize <= 0
            if (empty($chunk)) {
                continue;
            }

            try {
                Lead::upsert(
                    $chunk,
                    ['domain'], // Unique by columns
                    ['tld'] // Columns to update if record exists
                );
                Log::info('[LeadBatchProcessorHelper] Successfully upserted batch of '.count($chunk).' leads.');
            } catch (\Exception $e) {
                Log::error(
                    '[LeadBatchProcessorHelper] Error upserting batch of leads: '.$e->getMessage(),
                    ['batch_sample_first_domain' => $chunk[0]['domain'] ?? 'N/A']
                );
            }
        }
    }
}
