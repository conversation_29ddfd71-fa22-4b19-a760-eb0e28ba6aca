<?php

namespace App\Helpers;

// use App\Models\Lead; // This import appears unused. Consider removing if confirmed.
use Illuminate\Support\Facades\Log;

/**
 * Class WebsiteOnlineHelper
 *
 * Provides utility functions to determine if a website is online and whether it uses SSL.
 * It attempts to connect via HTTPS first, then falls back to HTTP if necessary.
 */
class WebsiteOnlineHelper // Renamed from HttpModeHelper
{
    /**
     * Maximum time in seconds that cURL functions are allowed to run.
     *
     * @const int
     */
    const CURL_TIMEOUT = 30;

    /**
     * Checks if a website is online and determines its SSL status.
     *
     * Tries connecting to the domain via HTTPS first. If that fails or doesn't result
     * in a successful HTTP status (2xx, 3xx), it tries HTTP.
     * Returns detailed status including final URL, HTTP code, and any cURL errors.
     *
     * @param  string  $domain  The domain name to check (e.g., "example.com").
     * @return array An associative array with the following keys:
     *               'success'   => (bool) True if a connection was made and HTTP status indicates online, false otherwise.
     *               'isOnline'  => (bool) True if the website is considered online (HTTP 2xx/3xx), false otherwise.
     *               'isSSL'     => (bool) True if the final successful URL is HTTPS, false otherwise.
     *               'final_url' => (string|null) The final URL after all redirects, or null on total failure.
     *               'http_code' => (int|null) The HTTP status code received, or null if no HTTP response.
     *               'error'     => (string|null) cURL error message if one occurred, or an HTTP status error, or null on success.
     */
    public static function getWebsiteOnlineStatus(string $domain): array
    {
        // Define the URLs to attempt, prioritizing HTTPS.
        $urlsToTry = [
            'https://'.$domain,
            'http://'.$domain,
        ];

        // Default result structure, assuming failure until a successful check proves otherwise.
        $defaultResult = ['success' => false, 'isOnline' => false, 'isSSL' => false, 'final_url' => null, 'http_code' => null, 'error' => 'No successful connection.'];

        foreach ($urlsToTry as $url) {
            Log::debug("[WebsiteOnlineHelper] Checking URL: {$url} for domain {$domain}");
            $ch = curl_init($url);

            // Configure cURL options.
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);      // Return the transfer as a string.
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);      // Follow HTTP redirects.
            curl_setopt($ch, CURLOPT_MAXREDIRS, 20);             // Limit the number of redirects.
            curl_setopt($ch, CURLOPT_TIMEOUT, self::CURL_TIMEOUT); // Maximum execution time for the request.
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, self::CURL_TIMEOUT / 2); // Connection timeout.
            curl_setopt($ch, CURLOPT_NOBODY, true);              // Use a HEAD request to get headers only (faster for status checks).
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'); // Set a user agent.

            // Add common browser headers
            $headers = [
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language: en-US,en;q=0.9',
            ];
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_ENCODING, ''); // Handles Accept-Encoding (e.g., gzip, deflate) and sends the header

            // For HTTPS requests, disable SSL certificate verification for broader compatibility (e.g., self-signed certs).
            // Note: This reduces security. For critical applications, consider more robust SSL validation.
            if (str_starts_with(strtolower($url), 'https')) {
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            }

            curl_exec($ch);
            $curlErrno = curl_errno($ch);

            // Check for cURL errors during the request execution.
            if ($curlErrno) {
                $curlError = curl_error($ch);
                Log::debug("[WebsiteOnlineHelper] cURL error for {$url} (Domain: {$domain}): [{$curlErrno}] {$curlError}");
                curl_close($ch);
                // Store the cURL error and continue to the next URL (e.g., from HTTPS to HTTP).
                $defaultResult['error'] = $curlError;

                continue;
            }

            // Retrieve HTTP status code and final URL after redirects.
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
            curl_close($ch);

            Log::debug("[WebsiteOnlineHelper] Response for {$url} (Domain: {$domain}): HTTP Code {$httpCode}, Final URL: {$finalUrl}");

            // Consider HTTP status codes 2xx (Success) and 3xx (Redirection) as indicators that the website is online.
            // cURL automatically follows 3xx redirects if CURLOPT_FOLLOWLOCATION is true.
            if ($httpCode >= 200 && $httpCode < 400) {
                return [
                    'success' => true,
                    'isOnline' => true,
                    'isSSL' => str_starts_with(strtolower($finalUrl ?? ''), 'https'),
                    'final_url' => $finalUrl,
                    'http_code' => $httpCode,
                    'error' => null, // No error on success.
                ];
            }

            // If this attempt didn't result in a cURL error but wasn't a 2xx/3xx response,
            // store its details in $defaultResult if it's the first such non-cURL error encountered.
            // This provides more specific feedback than 'No successful connection.' if all attempts fail with HTTP errors.
            if ($defaultResult['http_code'] === null && $httpCode > 0) {
                $defaultResult['final_url'] = $finalUrl;
                $defaultResult['http_code'] = $httpCode;
                $defaultResult['isSSL'] = str_starts_with(strtolower($finalUrl ?? ''), 'https');
                $defaultResult['error'] = 'HTTP status code '.$httpCode;
            } elseif ($httpCode == 0 && $defaultResult['http_code'] === null) {
                // Handle cases where $httpCode is 0 (e.g., connection timeout not caught by curl_errno).
                $defaultResult['error'] = 'No HTTP response (code 0), possibly connection timeout or other issue.';
            }
        }

        // If the loop completes, none of the URLs resulted in a successful (2xx/3xx) response.
        // Return the $defaultResult, which contains details of the first encountered HTTP error or the cURL error from the last attempt.
        return $defaultResult;
    }
}
