<?php

namespace App\Helpers;

class WebsitePlatformInformationHelper
{
    /**
     * Analyzes HTML content to extract platform, theme, and version information.
     *
     * @param  string|null  $ipAddress  (Currently unused in this version, but kept for signature consistency)
     * @param  string  $htmlContent  The HTML content of the website.
     * @return array An array containing platform_name, platform_version, theme_name, and theme_version.
     */
    public static function getWebsitePlatformInformation(?string $ipAddress, string $htmlContent): array
    {
        // Standard return data
        $data = [
            'platform_name' => null,
            'platform_version' => null,
            'theme_name' => null,
            'theme_version' => null,
        ];

        // Platform tests
        $platformTests = [
            // Shopify
            [
                'platform_name' => 'Shopify',
                'detect' => function ($html) {
                    return preg_match('/cdn\.shopify\.com/i', $html)
                        || preg_match('/<meta\s+name=["\']shopify-digital-wallet["\']/i', $html)
                        // Ensuring Shopify.theme object presence is a strong indicator
                        || preg_match('/Shopify\.theme\s*=\s*\{/i', $html);
                },
                'version' => function ($html) {
                    // Shopify platform version itself isn't typically exposed directly in public HTML.
                    // Specific app or theme versions are more relevant.
                    return null;
                },
                'theme' => function ($html) {
                    // Look for Shopify.theme = { ... "schema_name":"THEME_NAME" ... };
                    if (preg_match('/Shopify\.theme\s*=\s*\{[^}]*"schema_name"\s*:\s*"([^"]+)"[^}]*\}/is', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme_version' => function ($html) {
                    // Look for Shopify.theme = { ... "schema_version":"THEME_VERSION" ... };
                    if (preg_match('/Shopify\.theme\s*=\s*\{[^}]*"schema_version"\s*:\s*"([^"]+)"[^}]*\}/is', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
            ],
            // WooCommerce
            [
                'platform_name' => 'WooCommerce',
                'detect' => function ($html) {
                    return (preg_match('/woocommerce/i', $html) && strpos($html, '/wp-content/') !== false)
                        || preg_match('/class=["\']\s*woocommerce\s*["\']/i', $html);
                },
                'version' => function ($html) {
                    if (preg_match('/<!-- WooCommerce Version: ([0-9\.]+) -->/i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme' => function ($html) {
                    if (preg_match('/\/wp-content\/themes\/([^\/]+)\/style\.css/i', $html, $matches)) {
                        return $matches[1];
                    }
                    if (preg_match('/{"themeName":"([^"]+)"/i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme_version' => function ($html) {
                    if (preg_match('/{"version":"([^"]+)"/i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
            ],
            // WordPress
            [
                'platform_name' => 'WordPress',
                'detect' => function ($html) {
                    return preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']WordPress\s*([0-9\.]+)?["\']\s*\/?>/i', $html, $matches)
                        || strpos($html, '/wp-content/') !== false
                        || preg_match('/\/wp-includes\//i', $html);
                },
                'version' => function ($html) {
                    if (preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']WordPress\s*([0-9\.]+)["\']\s*\/?>/i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme' => function ($html) {
                    if (preg_match('/\/wp-content\/themes\/([^\/]+)\/style\.css/i', $html, $matches)) {
                        return $matches[1];
                    }
                    if (preg_match('/{"themeName":"([^"]+)"/i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme_version' => function ($html) {
                    if (preg_match('/{"version":"([^"]+)"/i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
            ],
            // Joomla
            [
                'platform_name' => 'Joomla',
                'detect' => function ($html) {
                    return preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']Joomla!.*?["\']\s*\/?>/i', $html);
                },
                'version' => function ($html) {
                    if (preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']Joomla!\s+([0-9\.]+)["\']/i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme' => function ($html) {
                    if (preg_match('/\/templates\/([^\/]+)\/css\/template\.css/i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme_version' => null,
            ],
            // Drupal
            [
                'platform_name' => 'Drupal',
                'detect' => function ($html) {
                    return preg_match('/<meta\s+name=["\']Generator["\']\s+content=["\']Drupal\s*([0-9\.]+)?["\']\s*\/?>/i', $html)
                        || preg_match('/data-drupal-selector/i', $html)
                        || preg_match('/drupal-core/i', $html);
                },
                'version' => function ($html) {
                    if (preg_match('/<meta\s+name=["\']Generator["\']\s+content=["\']Drupal\s+([0-9\.]+)["\']/i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme' => function ($html) {
                    if (preg_match('/\/themes\/([^\/]+)\/style\.css/i', $html, $matches)) {
                        return $matches[1];
                    }
                    if (preg_match('/\/themes\/custom\/([^\/]+)\/css\//i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme_version' => null,
            ],
            // Magento 2
            [
                'platform_name' => 'Magento 2',
                'detect' => function ($html) {
                    // Strong M2 indicators
                    if (
                        preg_match('/<script\s+type=["\']text\/x-magento-init["\']>/i', $html) ||
                        preg_match('/static\/frontend\/Magento\/(luma|blank)\//i', $html)
                    ) {
                        return true;
                    }
                    // M2 specific generator tag
                    if (preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']Magento\s+2/i', $html)) {
                        return true;
                    }

                    return false;
                },
                'version' => function ($html) {
                    if (preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']Magento\s+([2-9]\.[0-9\.]+([\.\-][\p{L}0-9]+)?)["\']/iu', $html, $matches)) {
                        return $matches[1];
                    }
                    if (preg_match('/window\.magentoVersion\s*=\s*\{.*"version":\s*"([^"]+)"/s', $html, $matches)) {
                        return $matches[1];
                    }
                    if (preg_match('/static\/version\d+/i', $html) || preg_match('/data-mage-init=/i', $html) || preg_match('/<script\s+type=["\']text\/x-magento-init["\']>/i', $html)) {
                        return '2.x';
                    }

                    return null;
                },
                'theme' => function ($html) {
                    if (preg_match('/static\/(?:version[0-9]+\/)?frontend\/([^\/"]+)\/([^\/"\?]+)\/(?:[a-zA-Z_]{2,5}(?:_[a-zA-Z_]{2,5})?\/)?/i', $html, $matches)) {
                        return $matches[1].'/'.$matches[2]; // Vendor/Theme
                    }

                    return null;
                },
                'theme_version' => null,
            ],
            // Magento 1
            [
                'platform_name' => 'Magento 1',
                'detect' => function ($html) {
                    // Generic Magento generator tag, assume M1 if M2 specific markers weren't found
                    if (preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']Magento/i', $html)) {
                        // Check explicitly it's not an M2 generator tag if relying on the generic "Magento"
                        if (! preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']Magento\s+2/i', $html)) {
                            return true;
                        }
                    }

                    return false;
                },
                'version' => function ($html) {
                    if (preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']Magento\s+([10]\.[0-9\.]+([\.\-][\p{L}0-9]+)?)["\']/iu', $html, $matches)) {
                        return $matches[1];
                    }
                    if (preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']Magento\s+([0-9\.]+)["\']/i', $html, $matches)) {
                        if (strpos($matches[1], '2.') !== 0) {
                            return $matches[1];
                        }
                    }
                    if (preg_match('/Magento\s+([0-9\.]+)\s+\((Enterprise|Community)\)/i', $html, $matches)) {
                        if (strpos($matches[1], '2.') !== 0) {
                            return $matches[1].' '.$matches[2];
                        }
                    }
                    if (preg_match('/class="[^"]*Magento(?:CE|EE|Version)-([1]\.[0-9\.]+[^"\s]*)"/i', $html, $matches)) {
                        return $matches[1];
                    }
                    if (preg_match('/copyright[^<]+Magento[^<]+([1]\.[0-9]{1,2}(?:\.[0-9]{1,2})?(?:\sCE|\sEE)?)/i', $html, $matches)) {
                        return trim($matches[1]);
                    }

                    return null;
                },
                'theme' => function ($html) {
                    if (preg_match('/\/skin\/frontend\/([^\/"]+)\/([^\/"\?]+)/i', $html, $matches)) {
                        return $matches[1].'/'.$matches[2]; // package/theme
                    }

                    return null;
                },
                'theme_version' => null,
            ],
            // PrestaShop
            [
                'platform_name' => 'PrestaShop',
                'detect' => function ($html) {
                    return preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']PrestaShop["\']/i', $html)
                        || preg_match('/var\s+prestashop\s*=/i', $html)
                        || strpos($html, '/modules/ps_') !== false;
                },
                'version' => function ($html) {
                    if (preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']PrestaShop\s+([0-9\.]+)["\']/i', $html, $matches)) {
                        return $matches[1];
                    }
                    if (preg_match('/var\s+prestashop\s*=\s*\{.*?"version"\s*:\s*"([0-9\.]+)"/s', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme' => function ($html) {
                    if (preg_match('/\/themes\/([^\/]+)\/assets\//i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme_version' => null,
            ],
            // OpenCart
            [
                'platform_name' => 'OpenCart',
                'detect' => function ($html) {
                    return preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']OpenCart["\']/i', $html);
                },
                'version' => function ($html) {
                    if (preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']OpenCart\s+([0-9\.]+)["\']/i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme' => function ($html) {
                    if (preg_match('/catalog\/view\/theme\/([^\/]+)\//i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme_version' => null,
            ],
            // BigCommerce
            [
                'platform_name' => 'BigCommerce',
                'detect' => function ($html) {
                    return preg_match('/bigcommerce\.com/i', $html);
                },
                'version' => null,
                'theme' => function ($html) {
                    if (preg_match('/assets\/js\/theme-bundle\.([^\.]+)\.js/i', $html, $matches)) {
                        return 'theme-'.$matches[1];
                    }

                    return null;
                },
                'theme_version' => null,
            ],
            // Wix
            [
                'platform_name' => 'Wix',
                'detect' => function ($html) {
                    return preg_match('/static\.wixstatic\.com/i', $html)
                        || preg_match('/wix-bon-appetit/i', $html)
                        || preg_match('/X-Wix-/i', $html);
                },
                'version' => null,
                'theme' => null,
                'theme_version' => null,
            ],
            // Squarespace
            [
                'platform_name' => 'Squarespace',
                'detect' => function ($html) {
                    return preg_match('/static[0-9]\.squarespace\.com/i', $html)
                        || preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']Squarespace["\']/i', $html);
                },
                'version' => function ($html) {
                    if (preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']Squarespace\s+([0-9\.]+)["\']/i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme' => null,
                'theme_version' => null,
            ],
            // TYPO3
            [
                'platform_name' => 'TYPO3',
                'detect' => function ($html) {
                    return preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']TYPO3/i', $html)
                        || preg_match('/typo3temp\/|typo3conf\//i', $html);
                },
                'version' => function ($html) {
                    if (preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']TYPO3\s+(?:CMS\s+)?([0-9\.]+)/i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme' => null,
                'theme_version' => null,
            ],
            // Shoporama
            [
                'platform_name' => 'Shoporama',
                'detect' => function ($html) {
                    return preg_match('/<meta\s+name=["\']generator["\']\s+content=["\']Shoporama["\']/i', $html);
                },
                'version' => null,
                'theme' => function ($html) {
                    if (preg_match('/\/private_templates\/[^\/]+\/([^\/]+)\/js\//i', $html, $matches)) {
                        return $matches[1];
                    }

                    return null;
                },
                'theme_version' => null,
            ],
            // Dandomain + SmartWeb, HostedShop, Wannafind, Scannet
            [
                'platform_name' => 'Dandomain',
                'detect' => function ($html) {
                    return preg_match('/<meta\s+name=["\']generator["\']\s+content=["\'](Dandomain|SmartWeb|HostedShop|Wannafind|Scannet)["\']/i', $html);
                },
                'version' => null,
                'theme' => function ($html) {
                    // Example: <link href="https://sw13716.sfstatic.io/_design/smartytemplates/store/template001/assets/css/libs.css">
                    if (preg_match('/\/_design\/smartytemplates\/store\/([^\/]+)\/assets\//i', $html, $matches)) {
                        return $matches[1]; // e.g., template001
                    }

                    return null;
                },
                'theme_version' => null,
            ],
            // Ideal Shop
            [
                'platform_name' => 'Ideal Shop',
                'detect' => function ($html) {
                    return preg_match('/<link[^>]+href=["\']https:\/\/cdn-main\.ideal\.shop\/[^"\']+/i', $html);
                },
                'version' => null, // Ideal Shop version detection not specified
                'theme' => function ($html) {
                    // Example: <link href="https://cdn-main.ideal.shop/combined/css/[THEME NAME]/f2ab71c76475a607a96982c71308c759d71100be35bed08a2ac7e46f80ca83a3.min.zstd.css" rel="stylesheet" type="text/css" crossorigin>
                    if (preg_match('/https:\/\/cdn-main\.ideal\.shop\/combined\/css\/([^\/]+)\//i', $html, $matches)) {
                        return $matches[1]; // e.g., [THEME NAME]
                    }

                    return null;
                },
                'theme_version' => null, // Ideal Shop theme version detection not specified
            ],
        ];

        foreach ($platformTests as $test) {
            if (call_user_func($test['detect'], $htmlContent)) {
                $data['platform_name'] = $test['platform_name'];
                if (isset($test['version']) && is_callable($test['version'])) {
                    $data['platform_version'] = call_user_func($test['version'], $htmlContent);
                }
                if (isset($test['theme']) && is_callable($test['theme'])) {
                    $data['theme_name'] = call_user_func($test['theme'], $htmlContent);
                }
                if (isset($test['theme_version']) && is_callable($test['theme_version'])) {
                    $data['theme_version'] = call_user_func($test['theme_version'], $htmlContent);
                }
                // If WordPress is detected, and WooCommerce was also detected, prefer WooCommerce as platform name
                if ($data['platform_name'] === 'WordPress' && $test['platform_name'] === 'WooCommerce') {
                    // This logic might need adjustment if WooCommerce isn't explicitly listed first or WordPress test is too general
                } elseif ($data['platform_name'] === 'WooCommerce' && $test['platform_name'] === 'WordPress') {
                    // If WooCommerce was already found, and this is the WordPress test, ensure platform_name remains WooCommerce
                    $data['platform_name'] = 'WooCommerce'; // Keep WooCommerce
                }

                // If the primary platform is found, break.
                // Exception: If WooCommerce is found, we still want to run WordPress checks for theme/version if not already found by Woo.
                if ($data['platform_name'] !== 'WooCommerce' || ($data['platform_name'] === 'WooCommerce' && $test['platform_name'] === 'WooCommerce')) {
                    break;
                }
            }
        }

        return $data;
    }
}
