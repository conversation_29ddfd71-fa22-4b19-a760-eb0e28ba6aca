<?php

namespace App\Filament\Resources\LeadResource\Widgets;

use App\Models\Lead;
use Filament\Widgets\ChartWidget;

class LeadTLDsPie extends ChartWidget
{
    protected static ?string $heading = 'TLDs';

    /**
     * Generate a consistent color code for a given string.
     */
    protected function stringToColorCode($string): string
    {
        $hash = md5($string);

        return '#'.substr($hash, 0, 6);
    }

    /**
     * Provide data for the pie chart.
     */
    protected function getData(): array
    {
        // Retrieve TLDs from Leads model
        $tldsFromDb = Lead::pluck('tld');

        // Filter out null or empty TLDs and then count occurrences
        $tldsCounts = $tldsFromDb
            ->filter(function ($tld) {
                return is_string($tld) && $tld !== '';
            })
            ->countBy()
            ->sortDesc(); // Sort by the count in descending order

        // Split TLDs into top 20 and others
        $topTlds = $tldsCounts->take(20);
        $otherTldsCount = $tldsCounts->skip(20)->sum();

        $chartData = $topTlds;
        $chartLabels = $topTlds->map(function ($count, $tld) {
            return "{$tld} ({$count})";
        });

        if ($otherTldsCount > 0) {
            $chartData = $chartData->put('Other', $otherTldsCount);
            $chartLabels = $chartLabels->push("Other ({$otherTldsCount})");
        }

        // Generate labels with counts and corresponding colors
        $labels = $chartLabels->values()->toArray();

        // Generate colors for each TLD using the hashing function
        $colors = $chartData->keys()->map(function ($tld) {
            return $this->stringToColorCode((string) $tld);
        })->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'TLDs',
                    'data' => $chartData->values()->toArray(),
                    'backgroundColor' => $colors,
                ],
            ],
            'labels' => $labels,
        ];
    }

    /**
     * Specify the type of chart.
     */
    protected function getType(): string
    {
        return 'pie';
    }
}
