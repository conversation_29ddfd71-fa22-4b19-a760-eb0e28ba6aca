<?php

namespace App\Filament\Resources\LeadResource\Widgets;

use App\Models\Lead;
use Filament\Widgets\ChartWidget;

class LeadShopifyPlatformPie extends ChartWidget
{
    protected static ?string $heading = 'Shopify by TLDs';

    protected static ?int $sort = 5;

    protected function stringToColorCode($string): string
    {
        $hash = md5($string);

        return '#'.substr($hash, 0, 6);
    }

    protected function getData(): array
    {
        // Retrieve TLDs directly from the database for Shopify leads
        $tldCounts = Lead::where('website_platform_name', 'Shopify')
            ->whereNotNull('tld')
            ->where('tld', '!=', '')
            ->where('tld', '!=', '0') // Assuming '0' is not a valid TLD
            ->pluck('tld')
            ->countBy()
            ->filter(function ($count, $tld) {
                return $count > 0 && $tld !== null && $tld !== '';
            })->sortDesc();

        $segmentColors = $tldCounts->keys()->map(function ($tld) {
            return $this->stringToColorCode($tld);
        })->toArray();

        $chartLabels = $tldCounts->map(function ($count, $tld) {
            return "{$tld} ({$count})";
        })->values()->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'TLDs for Shopify',
                    'data' => $tldCounts->values()->toArray(),
                    'backgroundColor' => $segmentColors,
                ],
            ],
            'labels' => $chartLabels,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
