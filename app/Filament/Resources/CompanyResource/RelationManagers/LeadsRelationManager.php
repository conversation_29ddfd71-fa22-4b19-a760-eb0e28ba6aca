<?php

namespace App\Filament\Resources\CompanyResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class LeadsRelationManager extends RelationManager
{
    protected static string $relationship = 'leads';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // We might not need a full form here if leads are primarily managed via LeadResource
                // For now, let's keep it simple or even remove if not needed for quick creation from company view
                Forms\Components\TextInput::make('website_url')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('domain')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('domain') // Or 'website_url'
            ->columns([
                Tables\Columns\TextColumn::make('website_url')
                    ->searchable(),
                Tables\Columns\TextColumn::make('domain')
                    ->searchable(),
                Tables\Columns\IconColumn::make('contact')
                    ->boolean(),
                Tables\Columns\IconColumn::make('relevant')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
                // Consider adding domain_status if it's a human-readable status
            ])
            ->filters([
                //
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(), // Enable if quick creation is desired
                Tables\Actions\AssociateAction::make(), // To link existing leads
            ])
            ->actions([
                Tables\Actions\ViewAction::make()->url(fn (\App\Models\Lead $record): string => \App\Filament\Resources\LeadResource::getUrl('view', ['record' => $record])),
                Tables\Actions\EditAction::make()->url(fn (\App\Models\Lead $record): string => \App\Filament\Resources\LeadResource::getUrl('edit', ['record' => $record])),
                Tables\Actions\DissociateAction::make(),
                // Tables\Actions\DeleteAction::make(), // Leads should generally be deleted via LeadResource
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DissociateBulkAction::make(),
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
