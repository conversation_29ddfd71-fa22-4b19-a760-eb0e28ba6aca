<?php

namespace App\Console\Commands;

use App\Helpers\DomainParserHelper;
use App\Models\Lead;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateLeadTldsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'leads:update-tlds {--chunk-size=1000 : The number of leads to process in each chunk}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the TLD for all leads by re-processing their domain using DomainParserHelper and upserting the changes.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting TLD update process for leads (using upsert)...');
        Log::info('[UpdateLeadTldsCommand] Starting TLD update process with upsert.');

        $chunkSize = (int) $this->option('chunk-size');
        if ($chunkSize <= 0) {
            $this->error('Chunk size must be a positive integer.');
            Log::error('[UpdateLeadTldsCommand] Invalid chunk size provided: '.$this->option('chunk-size'));

            return Command::FAILURE;
        }

        $updatedCount = 0;
        $totalProcessed = 0;
        $failedProcessingCount = 0; // For leads where TLD calculation fails
        $failedUpsertCount = 0; // For leads in batches that failed to upsert

        Lead::query()
            ->select(['id', 'domain']) // Select only necessary fields for processing
            ->chunkById($chunkSize, function ($leads) use (&$updatedCount, &$totalProcessed, &$failedProcessingCount, &$failedUpsertCount) {
                $this->info('Processing a chunk of '.$leads->count().' leads...');
                $batchData = [];

                foreach ($leads as $lead) {
                    $totalProcessed++;
                    try {
                        $domainInfo = DomainParserHelper::getProcessedDomainInformation($lead->domain);
                        $newTld = $domainInfo ? $domainInfo['tld'] : null;

                        $batchData[] = [
                            'id' => $lead->id,
                            'tld' => $newTld,
                        ];
                    } catch (\Exception $e) {
                        $this->error("Failed to calculate TLD for lead ID {$lead->id} (domain: {$lead->domain}): ".$e->getMessage());
                        Log::error("[UpdateLeadTldsCommand] Exception calculating TLD for lead ID {$lead->id} (domain: {$lead->domain}): ".$e->getMessage());
                        $failedProcessingCount++;
                    }
                }

                if (! empty($batchData)) {
                    try {
                        Lead::upsert($batchData, ['id'], ['tld']);
                        $updatedCount += count($batchData);
                        Log::info('[UpdateLeadTldsCommand] Batch upsert successful for '.count($batchData).' leads.');
                    } catch (\Exception $e) {
                        $this->error('Failed to batch upsert TLDs: '.$e->getMessage());
                        Log::error('[UpdateLeadTldsCommand] Exception during batch upsert for '.count($batchData).' leads: '.$e->getMessage());
                        // All leads in this batchData failed to upsert
                        $failedUpsertCount += count($batchData);
                    }
                }
                $this->info("Processed {$totalProcessed} leads so far. {$updatedCount} TLDs successfully upserted. {$failedProcessingCount} TLD calculations failed. {$failedUpsertCount} upserts failed within batches.");
            });

        $this->info('TLD update process completed.');
        $this->info("Total leads processed: {$totalProcessed}.");
        $this->info("Total TLDs successfully upserted: {$updatedCount}.");
        $this->info("Total TLD calculations failed: {$failedProcessingCount}.");
        $this->info("Total leads in failed upsert batches: {$failedUpsertCount}.");

        Log::info("[UpdateLeadTldsCommand] TLD update process finished. Processed: {$totalProcessed}, Upserted: {$updatedCount}, TLD Calc Fails: {$failedProcessingCount}, Upsert Batch Fails: {$failedUpsertCount}");

        if ($failedProcessingCount > 0 || $failedUpsertCount > 0) {
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
