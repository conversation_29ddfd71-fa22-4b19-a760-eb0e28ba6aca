<?php

namespace App\Console\Commands;

use App\Helpers\DomainParserHelper;
use App\Models\Lead;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use phpseclib3\File\X509;

class FetchLeadsDomainCertificates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:fetch-domain-logs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Domain Certificate Transparency Logs';

    /**
     * The URL for the CT log list.
     *
     * @var string
     */
    protected $logListUrl = 'https://www.gstatic.com/ct/log_list/v3/log_list.json';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Fetch the current logs from the log list
        $logServers = $this->fetchCurrentLogServers();
        if (empty($logServers)) {
            $this->error('No active CT logs found.');

            return 1;
        }

        // Iterate over each log server and fetch entries
        foreach ($logServers as $logServer) {
            $this->info("Fetching logs from: {$logServer}");
            $start = 0;
            $maxBlockSize = 32;
            while (true) {
                $end = $start + $maxBlockSize - 1;
                $entries = $this->fetchEntriesFromLog($logServer, $start, $end);
                if (empty($entries)) {
                    $this->info("No more entries available at {$logServer}.");
                    break;
                }

                // Process each entry
                foreach ($entries as $entry) {
                    $this->processEntry($entry);
                }

                // Update start for the next batch
                $start = $end + 1;
            }
        }

        return 0;
    }

    /**
     * Fetch the current active log servers from the log list.
     *
     * @throws Exception
     */
    private function fetchCurrentLogServers(): array
    {
        $response = Http::get($this->logListUrl);
        if ($response->failed()) {
            throw new Exception('Failed to fetch the CT log list.');
        }

        $data = $response->json();
        if (! isset($data['operators']) || ! is_array($data['operators'])) {
            throw new Exception('Invalid log list format.');
        }

        // Get the current date in ISO 8601 format
        $currentDate = date('Y-m-d\TH:i:s\Z');

        $logServers = [];
        foreach ($data['operators'] as $operator) {
            foreach ($operator['logs'] as $log) {
                // Check if the log is usable
                if (isset($log['state']['usable'])) {
                    $usableTimestamp = $log['state']['usable']['timestamp'];

                    // Check if the log's temporal interval includes the current date
                    $startInclusive = $log['temporal_interval']['start_inclusive'] ?? null;
                    $endExclusive = $log['temporal_interval']['end_exclusive'] ?? null;

                    if ($startInclusive && $endExclusive &&
                        $currentDate >= $startInclusive &&
                        $currentDate < $endExclusive) {

                        // Add the log URL to the list of active log servers
                        $logServers[] = rtrim($log['url'], '/'); // Remove trailing slash if present
                    }
                }
            }
        }

        if (empty($logServers)) {
            throw new Exception('No active logs available for the current time period.');
        }

        return $logServers;
    }

    /**
     * Fetch entries from a Certificate Transparency Log server.
     *
     * @throws Exception
     */
    private function fetchEntriesFromLog(string $logServer, int $start, int $end): array
    {
        $url = "{$logServer}/ct/v1/get-entries?start={$start}&end={$end}";
        $response = Http::get($url);

        if ($response->failed()) {
            throw new Exception("Failed to fetch data from the CTL server: {$logServer}");
        }

        $data = $response->json();
        if (! isset($data['entries'])) {
            throw new Exception("Invalid response format from {$logServer}");
        }

        return $data['entries'];
    }

    /**
     * Process a single CTL entry.
     */
    private function processEntry(array $entry): void
    {
        $leafInput = base64_decode($entry['leaf_input']);
        $extraData = base64_decode($entry['extra_data']);

        if ($leafInput === false || $extraData === false) {
            $this->warn('Failed to decode base64 data for an entry.');

            return;
        }

        // Parse the leaf input to extract the certificate
        $header = unpack('CVersion/CLeafType/JTimestamp/nLogEntryType', substr($leafInput, 0, 12));
        $entryType = $header['LogEntryType'];

        if ($entryType === 0) {
            $this->info('Processing X.509 certificate...');
            $certData = $this->extractCertificateData($leafInput);
            $certInfo = $this->certificateInfo($certData);
            $this->info(json_encode($certInfo, JSON_PRETTY_PRINT));
        } elseif ($entryType === 1) {
            $this->info('Processing PreCertificate...');
            $certData = $this->extractPreCertificateData($extraData);
            $certInfo = $this->certificateInfo($certData);
            $this->info(json_encode($certInfo, JSON_PRETTY_PRINT));
        } else {
            $this->warn('Unknown LogEntryType.');
        }
    }

    /**
     * Extract certificate data from an X.509 entry.
     */
    private function extractCertificateData(string $leafInput): ?string
    {
        $certLength = unpack('N', substr($leafInput, 12, 3)."\0")[1];
        $certData = substr($leafInput, 15, $certLength);

        return $certData ?: null;
    }

    /**
     * Extract certificate data from a PreCertificate entry.
     */
    private function extractPreCertificateData(string $extraData): ?string
    {
        $leafCertLength = unpack('N', substr($extraData, 0, 3)."\0")[1];
        $leafCertData = substr($extraData, 3, $leafCertLength);

        return $leafCertData ?: null;
    }

    /**
     * Display information about a certificate.
     */
    private function certificateInfo(?string $certData): ?array
    {
        if ($certData === null) {
            $this->warn('Certificate data is missing or could not be extracted.');

            return null;
        }

        $x509 = new X509;
        $cert = $x509->loadX509($certData);

        if ($cert === false) {
            $this->warn('Failed to parse the certificate.');

            return null;
        }

        // Get domains from the certificate
        $domains = [];
        foreach ($cert['tbsCertificate']['subject']['rdnSequence'] as $rdn) {
            foreach ($rdn as $typeAndValue) {
                if ($typeAndValue['type'] === 'id-at-commonName') {
                    $tmpDomain = implode('', $typeAndValue['value']);

                    // Remove .* from the domain, if present (e.g., *.example.com -> example.com)
                    if (str_starts_with($tmpDomain, '*.')) {
                        $tmpDomain = substr($tmpDomain, 2);
                    }
                    // Also handle cases like .* (which is less common for CN but good to be safe)
                    $tmpDomain = str_replace('.*', '', $tmpDomain);

                    // Use DomainParserHelper to get the registrable domain and other info
                    $domainInfo = DomainParserHelper::getProcessedDomainInformation($tmpDomain);

                    if ($domainInfo === null || empty($domainInfo['domain'])) {
                        Log::warning('Could not normalize or extract domain from certificate CN', ['original_cn' => $typeAndValue['value'], 'processed_cn' => $tmpDomain]);

                        continue;
                    }

                    $finalDomain = $domainInfo['domain'];
                    $websiteUrl = $domainInfo['website_url']; // Use website_url directly from helper

                    // Check if the domain is valid (DomainParserHelper already does some validation, but an explicit check is fine)
                    if (! filter_var($finalDomain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME)) {
                        Log::warning('Invalid domain after parsing from certificate', ['original_cn' => $typeAndValue['value'], 'parsed_domain' => $finalDomain]);

                        continue;
                    }

                    // Add the domain to the list
                    $domains[] = $finalDomain;

                    // Create a new lead and save it to the database
                    Lead::updateOrCreate(
                        ['domain' => $finalDomain],
                        [
                            'domain' => $finalDomain,
                            'website_url' => $websiteUrl, // Use the standardized https URL
                        ]);
                } else {
                    Log::warning('Invalid URL structure in result', ['result' => $parsedUrl]); // This 'else' might be unreachable now if $domainInfo checks cover it
                }
            }
        }

        return $domains;
    }
}
