<?php

namespace App\Console\Commands;

use App\Helpers\WebsiteOnlineHelper;
use App\Jobs\GetCompanyDataJob;
use App\Jobs\GetWebsiteInformationJob;
use App\Jobs\GetWebsitePageSpeedJob;
use App\Jobs\GetWebsiteWhoisJob;
use App\Models\Lead;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GetLeadData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:get-lead-data {--tld= : Optional TLD to filter leads by (e.g., com, org)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Processes up to 75 leads that are due for a data update (older than 12 hours or never updated).';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Log the start of the scheduled run.
        Log::info('[GetLeadDataCommand] Starting scheduled run...');
        $this->info('Processing a batch of up to 75 leads due for an update...');

        $tldFilter = $this->option('tld');

        if ($tldFilter) {
            Log::info("[GetLeadDataCommand] Filtering for TLD: {$tldFilter}");
            $this->info("Processing leads with TLD: {$tldFilter}");
        } else {
            Log::info('[GetLeadDataCommand] No TLD filter specified, processing all relevant leads.');
            $this->info('Processing leads (no TLD filter).');
        }

        // Fetch leads
        $query = Lead::query()->orderBy('data_updated');

        if ($tldFilter) {
            $query->where('tld', $tldFilter);
        }

        $leadsToProcess = $query->take(75)->get();

        // Check if there are any leads to process.
        if ($leadsToProcess->isEmpty()) {
            $this->info('No leads require processing at this time.');
            Log::info('[GetLeadDataCommand] No leads require processing in this batch.');

            return 0;
        }

        // Log the number of leads found for processing.
        $this->info("Found {$leadsToProcess->count()} leads to process.");
        Log::info("[GetLeadDataCommand] Found {$leadsToProcess->count()} leads to process.");

        // Initialize counters for processed leads.
        $processedCount = 0;
        $totalLeads = $leadsToProcess->count();

        // Process each lead.
        foreach ($leadsToProcess as $lead) {
            $processedCount++;
            $this->info("Processing lead {$processedCount} / {$totalLeads} (ID: {$lead->id}, Domain: {$lead->domain})");
            Log::info("[GetLeadDataCommand] Processing lead {$processedCount} / {$totalLeads} (ID: {$lead->id}, Domain: {$lead->domain})");

            // Get the website online status for the lead's domain.
            $statusInfo = WebsiteOnlineHelper::getWebsiteOnlineStatus($lead->domain);

            // Check if the website is online.
            if ($statusInfo['isOnline']) {
                // Log the website status and dispatch jobs to gather detailed data.
                $this->info("Lead ID: {$lead->id} - Website is online. Final URL: {$statusInfo['final_url']}, SSL: ".($statusInfo['isSSL'] ? 'Yes' : 'No'));
                Log::info("[GetLeadDataCommand] Lead ID: {$lead->id} - Website online. Final URL: {$statusInfo['final_url']}, SSL: ".($statusInfo['isSSL'] ? 'Yes' : 'No'));

                // Dispatch jobs to gather detailed data for the lead.
                $this->performLeadDataGathering($lead); // Dispatch jobs to gather detailed data for the lead.

                // Update the lead record with the website status.
                $lead->website_status = 1; // Mark website as online.
                $lead->website_ssl = $statusInfo['isSSL']; // Store SSL status (true/false).
                $lead->website_url = $statusInfo['final_url']; // Store the final URL.
                $lead->data_updated = now(); // Timestamp the update.
                $lead->save();
            } else {
                // Log the website status and update the lead record.
                $this->warn("Lead ID: {$lead->id} - Website is offline or an error occurred. No further data gathering jobs dispatched.");
                $errorDetail = $statusInfo['error'] ?? 'N/A';
                $httpCodeDetail = $statusInfo['http_code'] ?? 'N/A';
                Log::warning("[GetLeadDataCommand] Lead ID: {$lead->id} - Website offline or error. Detail: {$errorDetail}, HTTP Code: {$httpCodeDetail}");
                $lead->website_status = 0; // Mark website as offline or error.
                $lead->website_ssl = $statusInfo['isSSL'] ?? false; // Store SSL status (or false if undetermined during error).
                $lead->data_updated = now(); // Timestamp the update attempt.
                $lead->save();
            }
        }

        // Log the end of the scheduled run.
        Log::info('[GetLeadDataCommand] Finished scheduled run.');
        $this->info('GetLeadData command finished scheduled run.');

        return 0;
    }

    /**
     * Dispatches various jobs to gather detailed data for a given lead.
     * This method is called only if the lead's website is confirmed to be online.
     *
     * @param  Lead  $lead  The lead for which to dispatch data gathering jobs.
     */
    private function performLeadDataGathering(Lead $lead): void
    {
        $this->info("[GetLeadDataCommand] Dispatching data gathering jobs for Lead ID: {$lead->id}");
        Log::info("[GetLeadDataCommand] Dispatching data gathering jobs for Lead ID: {$lead->id}");

        try {
            // Dispatch the GetWebsiteWhoisJob.
            GetWebsiteWhoisJob::dispatch($lead->id);
            Log::info("[GetLeadDataCommand] GetWebsiteWhoisJob dispatched for lead ID: {$lead->id}");
        } catch (Exception $e) {
            // Log any errors dispatching the GetWebsiteWhoisJob.
            Log::error("[GetLeadDataCommand] Error dispatching GetWebsiteWhoisJob for lead ID {$lead->id}: ".$e->getMessage());
        }

        try {
            // Dispatch the GetCompanyDataJob.
            GetCompanyDataJob::dispatch($lead->id);
            Log::info("[GetLeadDataCommand] GetCompanyDataJob dispatched for lead ID: {$lead->id}");
        } catch (Exception $e) {
            Log::error("[GetLeadDataCommand] Error dispatching GetCompanyDataJob for lead ID {$lead->id}: ".$e->getMessage());
        }

        try {
            // Dispatch the GetWebsiteInformationJob
            GetWebsiteInformationJob::dispatch($lead->id);
            Log::info("[GetLeadDataCommand] GetWebsiteInformationJob dispatched for lead ID: {$lead->id}");
        } catch (Exception $e) {
            Log::error("[GetLeadDataCommand] Error dispatching GetWebsiteInformationJob for lead ID {$lead->id}: ".$e->getMessage());
        }

        try {
            // Dispatch the GetWebsitePageSpeedJob.
            GetWebsitePageSpeedJob::dispatch($lead->id);
            Log::info("[GetLeadDataCommand] GetWebsitePageSpeedJob dispatched for lead ID: {$lead->id}");
        } catch (Exception $e) {
            // Log any errors dispatching the GetWebsitePageSpeedJob.
            Log::error("[GetLeadDataCommand] Error dispatching GetWebsitePageSpeedJob for lead ID {$lead->id}: ".$e->getMessage());
        }
    }
}
