<?php

namespace App\Console\Commands;

use App\Jobs\FetchAdtractionJob;
use App\Jobs\FetchAwinJob;
use App\Jobs\FetchEmaerketLeadsJob;
use App\Jobs\FetchKelkooJob;
use App\Jobs\FetchMollyAppJob;
use App\Jobs\FetchPartnerAdsLeadsJob;
use App\Jobs\FetchViabillJob;
use App\Jobs\WebSearchLeadsJob;
use Illuminate\Console\Command;

class FetchLeadsDailyCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:fetch-leads-daily';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        FetchEmaerketLeadsJob::dispatch();
        FetchViabillJob::dispatch();

        FetchMollyAppJob::dispatch();

        FetchPartnerAdsLeadsJob::dispatch();
        FetchAdtractionJob::dispatch();
        FetchAwinJob::dispatch();
        FetchKelkooJob::dispatch();

        // Web search (Brave, Bing, Google)
        // WebSearchLeadsJob::dispatch();

        exit();

        // TODO

        // site:.mywebshop.io

        /*
        (1, 'ShopOrama', '***********|\\/packed_js\\?files=|\\/packed_css\\?files=', '************,*************', ''),
(2, 'Wannafind webshop', '<meta name=\\\"generator\\\" content=\\\"Wannafind\\\">', '', ''),
(5, 'Smartweb webshop', '<meta name=\\\"generator\\\" content=\\\"SmartWeb\\\">|\\.smartweb-static\\.com', '', ''),
(3, 'HostedShop', '<meta name=\\\"generator\\\" content=\\\"HostedShop\\\">', '', ''),
(6, 'DanDomain webshop', '\\/shop\\/frontpage\\.html', '', ''),
(4, 'HostedShop / Wannafind', '\\.hstatic\\.dk', '', ''),
(7, 'TDChosting webshop', 'webshop\\.tdchps\\.dk', '', ''),
(8, 'ScanNet WebShop2', '\\/\\/static\\.128secure\\.net\\/', '', ''),
(9, 'Golden Planet', '', '', ''),
(101, 'Magento', '', '', ''),
(10, 'Weebio', '\\/weebio\\.min\\.css', '', ''),
(150, 'Joomla - Virtuemart', '', '', ''),
(200, 'Wordpress - WooCommerce', '', '', ''),
(11, 'Shopify', '\\/\\/cdn\\.shopify\\.com\\/s\\/files', '', ''),
(102, 'PrestaShop', '', '', ''),
(12, 'Danaweb webshop', '', '', ''),
(1001, 'Danaweb website', '', '', ''),
(2001, 'TiigerMedia', '', '', ''),
(2002, 'Konxion', '', '', ''),
(13, 'Salecto', '\\/skin\\/frontend\\/salecto\\/', '', '');
         * */

        // Keywords (platform, engine)
        $keywords = [
            // Other
            [],

            // ShopOrama
            ['.shoporama.dk', 1, 1],
            ['*************', 1, 2],

            // HostedShop / Wannafind
            ['.hostedshop.dk', 4, 1],
            ['*************', 4, 2],

            // Smartweb webshop - danhost server
            ['.smart-web.dk', 5, 1],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['**************', 5, 2],
            ['*************', 5, 2],
            ['*************', 5, 2],
            ['*************', 5, 2],
            ['*************', 5, 2],
            ['195.178.14.88', 5, 2],
            ['195.178.14.89', 5, 2],

            // ScanNet WebShop2
            ['.srv100.webshopdemo.net', 8, 1],
            ['193.239.97.100', 8, 2],
            ['.srv102.webshopdemo.net', 8, 1],
            ['193.239.97.102', 8, 2],
            ['.srv103.webshopdemo.net', 8, 1],
            ['193.239.97.103', 8, 2],
            ['.srv104.webshopdemo.net', 8, 1],
            ['193.239.97.104', 8, 2],
            ['.srv105.webshopdemo.net', 8, 1],
            ['193.239.97.105', 8, 2],
            ['.srv106.webshopdemo.net', 8, 1],
            ['193.239.97.106', 8, 2],
            ['.srv107.webshopdemo.net', 8, 1],
            ['193.239.97.107', 8, 2],

            // DanDomain webshop
            ['.webshop8.dk', 6, 1],
            ['.shop51.dandomain.dk', 6, 1],
            ['94.143.8.101', 6, 2],
            ['.shop52.dandomain.dk', 6, 1],
            ['************', 6, 2],
            ['.shop53.dandomain.dk', 6, 1],
            ['************', 6, 2],
            ['.shop54.dandomain.dk', 6, 1],
            ['************', 6, 2],
            ['.shop55.dandomain.dk', 6, 1],
            ['************', 6, 2],
            ['************', 6, 2],
            ['************', 6, 2],
            ['************', 6, 2],
            ['************', 6, 2],
            ['************', 6, 2],
            ['************', 6, 2],

            // TDChosting webshop / itadel.dk
            ['*************', 7, 2],

            // Golden Planet
            ['**************', 9, 2],
            ['**************', 9, 2],
            ['***************', 9, 2],
            ['***************', 9, 2],
            ['***************', 9, 2],
            ['*************', 9, 2],
            ['**************', 9, 2],
            ['**************', 9, 2],
            ['**************', 9, 2],
            ['**************', 9, 2],
            ['**************', 9, 2],
            ['**************', 9, 2],
            ['**************', 9, 2],
            ['**************', 9, 2],
            ['*********', 9, 2],
            ['**********', 9, 2],
            ['**************', 9, 2],
            ['**********', 9, 2],
            ['**********', 9, 2],
            ['*********7', 9, 2],
            ['************', 9, 2],
            ['**************', 9, 2],

            // Weebio
            ['***********', 10, 2],

            // Salecto
            ['185.31.79.94', 13, 2],

            // Danaweb webshop - Netgroup A/S server
            ['193.8.38.31', 12, 2],
            ['193.8.38.32', 12, 2],
            ['193.8.38.33', 12, 2],
            ['***********', 12, 2],
            ['***********', 12, 2],
            ['***********', 12, 2],

            // Danaweb website - DanaWeb A/S server
            ['***********', 1001, 2],
            ['***********', 1001, 2],
            ['***********', 1001, 2],
            ['***********', 1001, 2],
            ['***********', 1001, 2],

            // TigerMedia
            ['**************', 2001, 2],
            ['**************', 2001, 2],

            // Zitcom.dk / Curanet.dk / 123hotel.dk hosting
            ['**************', 1101, 2],

            // Windows server
            ['**************', 1102, 2],
            ['**************', 1102, 2],
            ['*************', 1102, 2],
            ['************', 1102, 2],
            ['*************', 1102, 2],
            ['***********', 1102, 2],
            ['************', 1102, 2],
            ['************', 1102, 2],
            ['************3', 1102, 2],
            ['************', 1102, 2],
            ['************', 1102, 2],
            ['************', 1102, 2],
            ['************', 1102, 2],
            ['*************', 1102, 2],

            // Linux server
            ['**************', 1103, 2],
            ['**************', 1103, 2],
            ['**************', 1103, 2],
            ['*************', 1103, 2],
            ['*************', 1103, 2],
            ['**************', 1103, 2],
            ['***********9', 1103, 2],
            ['*************', 1103, 2],
            ['94.231.108.28', 1103, 2],
            ['94.231.109.37', 1103, 2],
            ['94.231.109.204', 1103, 2],
            ['185.21.40.19', 1103, 2],
            ['185.21.40.13', 1103, 2],
            ['185.21.40.199', 1103, 2],
            ['185.21.41.45', 1103, 2],
            ['185.21.41.69', 1103, 2],
            ['185.21.41.111', 1103, 2],
            ['************2', 1103, 2],
            ['185.21.41.178', 1103, 2],
            ['185.21.40.65', 1103, 2],
        ];

        if ($this->option('all')) {
            foreach ($keywords as $k => $v) {
                $this->runCron($k, $v);
                sleep(10);
            }
        } elseif ($this->option('id')) {
            $id = $this->option('id');
            $this->runCron($id, $keywords[$id]);
        } else {
            $id = rand(0, count($keywords) - 1);
            $this->runCron($id, $keywords[$id]);
        }

        return 0;
    }

    protected function runCron($id, $check)
    {
        $this->info('Running cron for ID: '.$id);

        if (! empty($check)) {
            if ($check[2] == 1) {
                $query = 'site:'.urlencode($check[0]);
                $this->crawler($id, $query, $check[1]);
            } elseif ($check[2] == 2) {
                $query = 'ip:'.urlencode($check[0]);
                $this->crawler($id, $query, $check[1]);
            }
        } else {
            $this->info('No check provided');
        }
    }

    private function crawler($id, string $query, $int) {}
}
