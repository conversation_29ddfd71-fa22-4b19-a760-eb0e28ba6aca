<?php

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;

class ListAllCertificates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:list-all-certificates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Domain Certificate Transparency Logs';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // All logs also old logs
        // $logListUrl = 'https://www.gstatic.com/ct/log_list/v3/all_logs_list.json';

        // Current logs only
        $logListUrl = 'https://www.gstatic.com/ct/log_list/v3/log_list.json';

        // Fetch the CT log list
        $logListResponse = file_get_contents($logListUrl);
        if ($logListResponse === false) {
            exit('Failed to fetch log list.');
        }

        // Decode the JSON response
        $ctlLog = json_decode($logListResponse, true);
        if ($ctlLog === null) {
            exit('Failed to decode JSON.');
        }

        $totalCerts = 0;

        // Iterate through each operator's logs to get the number of certificates
        foreach ($ctlLog['operators'] as $operator) {
            foreach ($operator['logs'] as $log) {
                $logUrl = $log['url'];
                $logInfoUrl = "{$logUrl}ct/v1/get-sth";

                // Try to fetch the number of certificates from the log
                try {
                    $logInfoResponse = @file_get_contents($logInfoUrl);
                    if ($logInfoResponse === false) {
                        continue; // Skip if the request fails
                    }

                    $logInfo = json_decode($logInfoResponse, true);
                    if ($logInfo === null || ! isset($logInfo['tree_size'])) {
                        continue; // Skip if the response is not valid JSON or tree_size is missing
                    }

                    // Add to the total number of certificates
                    $treeSize = (int) $logInfo['tree_size'];
                    $totalCerts += $treeSize;

                    // Print the number of certificates for this log
                    echo "{$log['description']} ({$logUrl}) has ".number_format($treeSize, 0, '', ',').' certificates'.PHP_EOL;
                } catch (Exception $e) {
                    // Ignore exceptions and continue with the next log
                    continue;
                }
            }
        }

        // Print the total number of certificates
        echo 'Total certs -> '.number_format($totalCerts, 0, '').PHP_EOL;
    }
}
