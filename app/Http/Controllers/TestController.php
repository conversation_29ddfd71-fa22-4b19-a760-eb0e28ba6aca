<?php

namespace App\Http\Controllers;

use App\Helpers\WebAnalyzerHelper;
use Illuminate\Http\Request;

class TestController extends Controller
{
    /**
     * Test the analyzeWebsite method from WebAnalyzerHelper.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function runTest(Request $request)
    {
        $url = $request->input('url', 'https://kukuk.dk'); // Default URL for testing

        if (empty($url) || ! filter_var($url, FILTER_VALIDATE_URL)) {
            return response()->json(['error' => 'Invalid or missing URL.'], 400);
        }

        try {
            $analyzer = new WebAnalyzerHelper;
            $analysisResult = $analyzer->analyzeWebsite($url);

            return response()->json($analysisResult);
        } catch (\Exception $e) {
            return response()->json(['error' => 'An error occurred during analysis: '.$e->getMessage()], 500);
        }
    }
}
