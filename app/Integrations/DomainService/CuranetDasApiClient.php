<?php

namespace App\Integrations\DomainService;

use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CuranetDasApiClient
{
    /**
     * The base URL for the DAS API.
     *
     * @var string
     */
    protected $baseUrl;

    /**
     * The client ID for authentication.
     *
     * @var string|null
     */
    protected $clientId;

    /**
     * The client secret for authentication.
     *
     * @var string|null
     */
    protected $secret;

    /**
     * The default timeout for API requests in seconds.
     *
     * @var int
     */
    protected $defaultTimeout = 30;

    /**
     * Create a new DAS API client instance.
     */
    public function __construct(?string $baseUrl = null, ?string $clientId = null, ?string $secret = null)
    {
        $this->baseUrl = $baseUrl ?? config('services.curanet.base_url');
        $this->clientId = $clientId ?? config('services.curanet.client_id');
        $this->secret = $secret ?? config('services.curanet.secret');
    }

    /**
     * Check domain availability for up to 10 domains at a time.
     */
    public function checkDomainAvailability(array $domains, ?int $timeoutInSeconds = null): array
    {
        // Ensure we don't exceed the API limit of 10 domains
        if (count($domains) > 10) {
            Log::warning('Curanet DAS API: Attempted to check more than 10 domains. Only the first 10 will be checked.');
            $domains = array_slice($domains, 0, 10);
        }

        $timeout = $timeoutInSeconds ?? $this->defaultTimeout;

        // Ensure timeout is within the allowed range (1-30 seconds)
        $timeout = max(1, min(30, $timeout));

        $payload = [
            'timeoutInSeconds' => $timeout,
            'domains' => $domains,
        ];

        try {
            $response = $this->makeRequest('POST', '/das/v1/DomainTest', $payload);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Curanet DAS API Error: '.$response->body(), [
                'status' => $response->status(),
                'domains' => $domains,
            ]);

            return [
                'complete' => false,
                'dasResults' => array_map(function ($domain) {
                    return [
                        'domain' => $domain,
                        'status' => 'Error',
                        'errorMessage' => 'API request failed',
                    ];
                }, $domains),
            ];
        } catch (\Exception $e) {
            Log::error('Curanet DAS API Exception: '.$e->getMessage(), [
                'domains' => $domains,
            ]);

            return [
                'complete' => false,
                'dasResults' => array_map(function ($domain) use ($e) {
                    return [
                        'domain' => $domain,
                        'status' => 'Error',
                        'errorMessage' => 'Exception: '.$e->getMessage(),
                    ];
                }, $domains),
            ];
        }
    }

    /**
     * Make an HTTP request to the DAS API.
     */
    protected function makeRequest(string $method, string $endpoint, array $data = []): Response
    {
        $url = rtrim($this->baseUrl, '/').'/'.ltrim($endpoint, '/');

        $headers = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];

        // Add authentication headers if credentials are provided
        if ($this->clientId && $this->secret) {
            $headers['X-Client-ID'] = $this->clientId;
            $headers['X-Client-Secret'] = $this->secret;
        }

        return Http::withHeaders($headers)
            ->timeout($this->defaultTimeout)
            ->$method($url, $data);
    }
}
