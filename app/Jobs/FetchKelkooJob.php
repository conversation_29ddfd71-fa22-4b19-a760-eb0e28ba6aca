<?php

namespace App\Jobs;

use App\Helpers\DomainParserHelper;
use App\Helpers\LeadBatchProcessorHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FetchKelkooJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct() {}

    public function handle(): void
    {
        Log::info('[FetchKelkooJob] Starting job.');
        $allParsedLeads = [];
        $totalLeadsCollected = 0;

        // $countries = ['ae', 'at', 'au', 'be', 'br', 'ca', 'ch', 'cz', 'de', 'dk', 'es', 'fi', 'fr', 'gr', 'hk', 'hu', 'id', 'ie', 'in', 'it', 'jp', 'kr', 'mx', 'my', 'nb', 'nl', 'no', 'nz', 'ph', 'pl', 'pt', 'ro', 'se', 'sg', 'sk', 'tr', 'uk', 'us', 'vn', 'za'];

        $countries = ['dk'];

        $baseApiUrl = 'https://api.kelkoogroup.net/publisher/shopping/v2/feeds/merchants';

        foreach ($countries as $country) {
            Log::info("[FetchKelkooJob] Processing country: {$country}");
            try {
                $response = Http::timeout(300)->withHeaders([
                    'Accept-Encoding' => 'gzip',
                    'Authorization' => 'Bearer '.config('crawler.kelkoo_api_key'),
                ])->get($baseApiUrl, [
                    'country' => $country,
                    'format' => 'json',
                ]);
            } catch (\Exception $e) {
                Log::error('[FetchKelkooJob] An error occurred while fetching merchants for country '.$country, ['message' => $e->getMessage()]);

                continue;
            }

            if ($response->failed()) {
                Log::error('[FetchKelkooJob] Failed to fetch merchants for country '.$country.': '.$response->status());

                continue;
            }

            $merchantsData = $response->json();
            $merchantsList = null;

            // Check if the response itself is an array of merchants (e.g., for 'dk')
            // A simple heuristic: check if the first element is an array and has an 'id' key.
            if (is_array($merchantsData) && ! empty($merchantsData) && isset($merchantsData[0]['id'])) {
                $merchantsList = $merchantsData;
            }
            // Otherwise, check for the standard structure with a 'merchants' key
            elseif (isset($merchantsData['merchants']) && is_array($merchantsData['merchants'])) {
                $merchantsList = $merchantsData['merchants'];
            }

            if ($merchantsList !== null) {
                foreach ($merchantsList as $merchant) {
                    if (isset($merchant['url']) && ! empty(trim($merchant['url']))) {
                        $rawUrl = trim($merchant['url']);
                        $domainInfo = DomainParserHelper::getProcessedDomainInformation($rawUrl);

                        if ($domainInfo) {
                            $allParsedLeads[] = $domainInfo;
                            $totalLeadsCollected++;
                        } else {
                            Log::warning('[FetchKelkooJob] Could not parse domain info from merchant URL. Skipping.', ['merchant_url' => $rawUrl, 'country' => $country]);
                        }
                    } else {
                        Log::warning('[FetchKelkooJob] Missing or empty merchant URL for country '.$country.'. Skipping.', ['merchant_data_sample' => $merchant]);
                    }
                }
            } elseif (isset($merchantsData['message'])) {
                Log::error('[FetchKelkooJob] API error for country '.$country.': '.$merchantsData['message'], ['response_body' => $merchantsData]);
            } else {
                Log::info("[FetchKelkooJob] No merchants found or invalid format for country {$country}. Response: ".json_encode($merchantsData));
            }
        }

        if (! empty($allParsedLeads)) {
            Log::info('[FetchKelkooJob] Passing '.count($allParsedLeads).' collected leads to batch processor.');
            $batchProcessor = new LeadBatchProcessorHelper;
            $batchProcessor->processLeads($allParsedLeads);
        } else {
            Log::info('[FetchKelkooJob] No leads collected to process.');
        }

        Log::info("[FetchKelkooJob] Job finished. Total leads prepared for batching: {$totalLeadsCollected}");
    }
}
