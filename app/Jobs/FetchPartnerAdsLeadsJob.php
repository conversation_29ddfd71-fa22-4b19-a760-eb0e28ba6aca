<?php

namespace App\Jobs;

use App\Helpers\DomainParserHelper;
use App\Helpers\LeadBatchProcessorHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Client\Pool;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FetchPartnerAdsLeadsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct() {}

    public function handle(): void
    {
        Log::info('[FetchPartnerAdsLeadsJob] Starting job.');
        $allParsedLeads = [];
        $totalLeadsCollected = 0;

        // List of country codes to support DK, SE, NO
        $countryCodes = ['dk', 'se', 'no'];
        $apiKey = config('crawler.partner_ads_api_key');

        // Prepare URLs for the pool request
        $urls = [];
        foreach ($countryCodes as $countryCode) {
            $urls[] = "https://www.partner-ads.com/{$countryCode}/programoversigt_xml.php?key={$apiKey}";
        }

        // Make multiple API calls concurrently using Laravel's Http pool method
        try {
            $responses = Http::timeout(300)->pool(fn (Pool $pool) => collect($urls)->map(fn ($url) => $pool->retry(3, 9000)->get($url))->toArray()
            );
        } catch (\Exception $e) {
            Log::error('[FetchPartnerAdsLeadsJob] An error occurred while executing the pool request', ['message' => $e->getMessage()]);

            return;
        }

        // Handle each response from the HTTP pool
        foreach ($responses as $index => $response) {
            $countryCode = $countryCodes[$index];
            Log::info("[FetchPartnerAdsLeadsJob] Processing response for country: {$countryCode}");

            // Check if the response is an exception
            if ($response instanceof \Throwable) {
                Log::error('[FetchPartnerAdsLeadsJob] Error fetching data for '.$countryCode, ['exception' => $response]);

                continue;
            }

            // Check if the response is valid
            if ($response->failed()) {
                Log::error('[FetchPartnerAdsLeadsJob] Failed to fetch data for '.$countryCode.': '.$response->status());

                continue;
            }

            // Decode the content to the required charset
            $xmlData = iconv('ISO-8859-1', 'UTF-8', $response->body());
            if ($xmlData === false) {
                Log::error("[FetchPartnerAdsLeadsJob] Failed to convert XML data encoding for {$countryCode}.");

                continue;
            }

            // Parse XML to PHP array
            // Suppress errors during XML loading and check result, as malformed XML can happen
            libxml_use_internal_errors(true);
            $xmlObject = simplexml_load_string($xmlData, 'SimpleXMLElement', LIBXML_NOCDATA);
            libxml_clear_errors();

            if ($xmlObject === false) {
                Log::error("[FetchPartnerAdsLeadsJob] Failed to parse XML for {$countryCode}. XML Data (first 500 chars): ".substr($xmlData, 0, 500));

                // Log libxml errors if needed for deeper debugging
                // foreach(libxml_get_errors() as $error) { Log::error("[FetchPartnerAdsLeadsJob] XML Error: " . $error->message); }
                continue;
            }

            $json = json_decode(json_encode($xmlObject), true);

            if (isset($json['program']) && is_array($json['program'])) {
                // Ensure 'program' is always an array of programs, even if only one is returned
                $programs = isset($json['program'][0]) ? $json['program'] : [$json['program']];

                foreach ($programs as $merchant) {
                    if (isset($merchant['programurl']) && ! empty(trim($merchant['programurl']))) {
                        $rawUrl = trim($merchant['programurl']);
                        $domainInfo = DomainParserHelper::getProcessedDomainInformation($rawUrl);

                        if ($domainInfo) {
                            $allParsedLeads[] = $domainInfo;
                            $totalLeadsCollected++;
                        } else {
                            Log::warning("[FetchPartnerAdsLeadsJob] Could not parse domain info from programurl for a merchant in {$countryCode}. Skipping.", ['programurl' => $rawUrl]);
                        }
                    } else {
                        Log::warning("[FetchPartnerAdsLeadsJob] Missing or empty programurl for a merchant in {$countryCode}. Skipping.", ['merchant_data_sample' => $merchant]);
                    }
                }
            } else {
                Log::info("[FetchPartnerAdsLeadsJob] No 'program' data or invalid structure in XML for {$countryCode}");
            }
        }

        if (! empty($allParsedLeads)) {
            Log::info('[FetchPartnerAdsLeadsJob] Passing '.count($allParsedLeads).' collected leads to batch processor.');
            $batchProcessor = new LeadBatchProcessorHelper;
            $batchProcessor->processLeads($allParsedLeads);
        } else {
            Log::info('[FetchPartnerAdsLeadsJob] No leads collected to process.');
        }

        Log::info("[FetchPartnerAdsLeadsJob] Job finished. Total leads prepared for batching: {$totalLeadsCollected}");
    }
}
