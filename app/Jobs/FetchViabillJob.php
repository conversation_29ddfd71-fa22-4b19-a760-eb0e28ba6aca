<?php

namespace App\Jobs;

use App\Helpers\DomainParserHelper;
use App\Helpers\LeadBatchProcessorHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FetchViabillJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct() {}

    public function handle(): void
    {
        Log::info('[FetchViabillJob] Starting job.');
        $allParsedLeads = [];
        $processedCount = 0;

        $baseApiUrl = 'https://api-aws.viabill.com/marketing/merchant/search';
        $limit = 100;
        $offset = 0;
        $total = null;

        do {
            $apiUrl = "$baseApiUrl?limit=$limit&offset=$offset";
            Log::info("[FetchViabillJob] Fetching merchants. URL: {$apiUrl}");

            try {
                $response = Http::timeout(300)->get($apiUrl);
            } catch (\Exception $e) {
                Log::error('[FetchViabillJob] An error occurred while fetching merchants for offset '.$offset, ['message' => $e->getMessage()]);
                break;
            }

            if ($response->failed()) {
                Log::error('[FetchViabillJob] Failed to fetch merchants for offset '.$offset.'. Status: '.$response->status(), ['response_body' => $response->body()]);
                break;
            }

            $data = $response->json();

            if ($total === null && isset($data['total'])) {
                $total = (int) $data['total'];
                Log::info("[FetchViabillJob] Total merchants to fetch (approximate): {$total}");
            }

            if (isset($data['webshops']) && is_array($data['webshops'])) {
                if (empty($data['webshops']) && $offset > 0 && $total !== null && $offset < $total) {
                    Log::info("[FetchViabillJob] No more webshops returned at offset {$offset}, though total was {$total}. Ending Viabill fetch early.");
                    break;
                }
                if (empty($data['webshops']) && $total === 0) {
                    Log::info('[FetchViabillJob] No webshops found (total is 0).');
                    break;
                }

                foreach ($data['webshops'] as $shop) {
                    if (isset($shop['cleanUrl']) && ! empty(trim($shop['cleanUrl']))) {
                        $rawUrl = trim($shop['cleanUrl']);
                        $domainInfo = DomainParserHelper::getProcessedDomainInformation($rawUrl);

                        if ($domainInfo) {
                            $allParsedLeads[] = $domainInfo;
                            $processedCount++;
                        } else {
                            Log::warning('[FetchViabillJob] Could not parse domain info from cleanUrl. Skipping.', ['cleanUrl' => $rawUrl]);
                        }
                    } else {
                        Log::warning('[FetchViabillJob] Missing or empty cleanUrl in merchant data. Skipping.', ['shop_data' => $shop]);
                    }
                }
                Log::info("[FetchViabillJob] Processed page: offset {$offset}, limit {$limit}. Current cumulative processed from API: ".($offset + count($data['webshops'])).'. Leads collected so far: '.$processedCount);

            } else {
                Log::warning('[FetchViabillJob] Unexpected response format or no webshops array for offset '.$offset, ['response_sample' => substr(json_encode($data), 0, 200)]);
                if ($total === null || ($total !== null && $offset < $total)) {
                    Log::info('[FetchViabillJob] Breaking loop due to unexpected response or missing webshops array before expected end.');
                    break;
                }
            }

            $offset += $limit;

            $shouldContinue = ($total === null && ! empty($data['webshops'])) || ($total !== null && $offset < $total);
            if ($shouldContinue) {
                sleep(1);
            }

        } while ($shouldContinue);

        // Pass all collected leads to the batch processor
        if (! empty($allParsedLeads)) {
            Log::info('[FetchViabillJob] Passing '.count($allParsedLeads).' collected leads to batch processor.');
            $batchProcessor = new LeadBatchProcessorHelper;
            $batchProcessor->processLeads($allParsedLeads);
        } else {
            Log::info('[FetchViabillJob] No leads collected to process.');
        }

        Log::info("[FetchViabillJob] Job finished. Total leads prepared for batching: {$processedCount}");
    }
}
