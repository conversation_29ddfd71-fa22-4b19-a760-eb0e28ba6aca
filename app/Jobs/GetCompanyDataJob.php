<?php

namespace App\Jobs;

use App\Helpers\CompanyHelper;
use App\Models\Lead;
use Exception;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GetCompanyDataJob implements ShouldBeUnique, ShouldQueue // Renamed class
{
    use InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;

    public $backoff = 60;

    public $leadId;

    const MAX_DAILY_CALLS = 50;

    const REQUEST_TIMEOUT = 30;

    public function __construct($leadId)
    {
        $this->leadId = $leadId;
    }

    public function uniqueId(): string
    {
        return 'company_data_'.$this->leadId; // Updated uniqueId prefix
    }

    public function handle(): void
    {
        Log::info('[GetCompanyDataJob] Processing lead ID: '.$this->leadId); // Reflect new job name
        $lead = Lead::find($this->leadId);

        if (! $lead) {
            Log::error('[GetCompanyDataJob] Lead not found with ID: '.$this->leadId);

            return;
        }

        try {
            CompanyHelper::fetchAndUpdateCompanyInfo($lead);
            Log::info('[GetCompanyDataJob] CompanyHelper::fetchAndUpdateCompanyInfo called for lead ID: '.$this->leadId);
        } catch (Exception $e) {
            Log::error('[GetCompanyDataJob] Exception during Company Data lookup for lead ID '.$this->leadId.': '.$e->getMessage());
            if ($this->attempts() < $this->tries) {
                $this->release($this->backoff * $this->attempts());

                return;
            }
            throw $e;
        }
    }
}
