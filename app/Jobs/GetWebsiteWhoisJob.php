<?php

namespace App\Jobs;

use App\Helpers\WhoisHelper;
use App\Models\Lead;
use Exception;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GetWebsiteWhoisJob implements ShouldBeUnique, ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;

    public $backoff = 60;

    public $leadId;

    public function __construct($leadId)
    {
        $this->leadId = $leadId;
    }

    public function uniqueId(): string
    {
        return 'website_whois_'.$this->leadId;
    }

    public function handle(): void
    {
        Log::info('[GetWebsiteWhoisJob] Processing lead ID: '.$this->leadId);
        $lead = Lead::find($this->leadId);

        if (! $lead) {
            Log::error('[GetWebsiteWhoisJob] Lead not found with ID: '.$this->leadId);

            return;
        }

        if (empty($lead->domain)) {
            Log::error('[GetWebsiteWhoisJob] Domain is empty for lead ID: '.$this->leadId.'. Helper will also check.');

            return;
        }

        try {
            WhoisHelper::fetchAndUpdateWhoisInfo($lead);
            Log::info('[GetWebsiteWhoisJob] WhoisHelper::fetchAndUpdateWhoisInfo called for lead ID: '.$this->leadId);
        } catch (Exception $e) {
            Log::error('[GetWebsiteWhoisJob] Exception during WHOIS processing for lead ID '.$this->leadId.': '.$e->getMessage());
            if ($this->attempts() < $this->tries) {
                $this->release($this->backoff * $this->attempts());

                return;
            }
            throw $e;
        }
    }
}
