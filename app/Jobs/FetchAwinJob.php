<?php

namespace App\Jobs;

use App\Helpers\DomainParserHelper;
use App\Helpers\LeadBatchProcessorHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FetchAwinJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string $publisherId;

    protected ?string $apiKey;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->publisherId = '1195242';
        $this->apiKey = config('crawler.awin_api_key');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('[FetchAwinJob] Starting job.');

        if (empty($this->apiKey)) {
            Log::error('[FetchAwinJob] Awin API key is not configured. Ensure CRAWLER_AWIN_API_KEY is set in .env or awin_api_key in config/crawler.php.');

            return;
        }

        $allParsedLeads = [];
        $totalLeadsCollected = 0;

        $apiUrl = "https://api.awin.com/publishers/{$this->publisherId}/programmes";

        try {
            $response = Http::timeout(300)
                ->withHeaders([
                    'Authorization' => 'Bearer '.$this->apiKey,
                ])
                ->get($apiUrl, [
                    'accessToken' => $this->apiKey]);
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error('[FetchAwinJob] Awin API request failed.', [
                'message' => $e->getMessage(),
                'url' => $apiUrl,
                'response_body' => $e->response ? $e->response->body() : null,
            ]);

            return;
        } catch (\Exception $e) {
            Log::error('[FetchAwinJob] An unexpected error occurred while making the request to Awin API', [
                'message' => $e->getMessage(),
                'url' => $apiUrl,
            ]);

            return;
        }

        if ($response->failed()) {
            Log::error('[FetchAwinJob] Failed to fetch data from Awin API.', [
                'status' => $response->status(),
                'body' => $response->body(),
                'url' => $apiUrl,
            ]);

            return;
        }

        $programs = $response->json();

        if (is_array($programs)) {
            foreach ($programs as $program) {
                $displayUrl = $program['displayUrl'] ?? null;
                if (! empty($displayUrl)) {
                    $rawUrl = trim($displayUrl);
                    $domainInfo = DomainParserHelper::getProcessedDomainInformation($rawUrl);
                    if ($domainInfo) {
                        $allParsedLeads[] = $domainInfo;
                        $totalLeadsCollected++;
                    } else {
                        Log::warning('[FetchAwinJob] Could not parse domain info from displayUrl.');
                    }
                }
            }
        } else {
            Log::warning('[FetchAwinJob] No programs found or invalid format from Awin API.', ['response_sample' => substr($response->body() ?? '', 0, 500)]);
        }

        if (! empty($allParsedLeads)) {
            Log::info('[FetchAwinJob] Passing '.count($allParsedLeads).' collected leads to batch processor.');
            $batchProcessor = new LeadBatchProcessorHelper;
            $batchProcessor->processLeads($allParsedLeads);
        } else {
            Log::info('[FetchAwinJob] No leads collected to process.');
        }
        Log::info("[FetchAwinJob] Job finished. Total unique leads prepared for batching: {$totalLeadsCollected}");
    }
}
