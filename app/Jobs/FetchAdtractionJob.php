<?php

namespace App\Jobs;

use App\Helpers\DomainParserHelper;
use App\Helpers\LeadBatchProcessorHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FetchAdtractionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct() {}

    public function handle(): void
    {
        Log::info('[FetchAdtractionJob] Starting job.');
        $allParsedLeads = [];
        $totalLeadsCollected = 0;

        // Fetch all countries from the Adtraction markets API
        $marketApiUrl = 'https://api.adtraction.com/v2/partner/markets/';
        try {
            $marketResponse = Http::timeout(300)->withHeaders(['X-Token' => config('crawler.adtraction_api_key')])->get($marketApiUrl);
        } catch (\Exception $e) {
            Log::error('[FetchAdtractionJob] An error occurred while making the request to Adtraction Markets API', ['message' => $e->getMessage()]);

            return;
        }

        // Check if the market response is valid
        if ($marketResponse->failed()) {
            Log::error('[FetchAdtractionJob] Failed to fetch data from Adtraction Markets API: '.$marketResponse->status());

            return;
        }

        // Parse the JSON response
        $markets = $marketResponse->json();

        if (is_array($markets)) {
            foreach ($markets as $market) {
                if (isset($market['market'])) {
                    $countryCode = $market['market'];

                    Log::info("[FetchAdtractionJob] Processing market: {$countryCode}");

                    // Fetch programs for each country
                    $programApiUrl = 'https://api.adtraction.com/v3/partner/programs/';
                    $postData = [
                        'market' => $countryCode,
                    ];

                    try {
                        $programResponse = Http::timeout(300)->withHeaders(['X-Token' => config('crawler.adtraction_api_key')])->post($programApiUrl, $postData);
                    } catch (\Exception $e) {
                        Log::error('[FetchAdtractionJob] An error occurred while fetching programs for market '.$countryCode, ['message' => $e->getMessage()]);

                        continue;
                    }

                    // Check if the response is valid
                    if ($programResponse->failed()) {
                        Log::error('[FetchAdtractionJob] Failed to fetch programs for market '.$countryCode.': '.$programResponse->status());

                        continue;
                    }

                    // Parse the JSON response
                    $programs = $programResponse->json();

                    if (is_array($programs)) {
                        foreach ($programs as $program) {
                            if (isset($program['programId'], $program['programURL'])) {
                                $programUrl = trim($program['programURL']);
                                if (empty($programUrl)) {
                                    Log::warning('[FetchAdtractionJob] Empty programURL for program ID '.$program['programId'].' in market '.$countryCode.'. Skipping.');

                                    continue;
                                }

                                $rawUrl = trim($programUrl);
                                $domainInfo = DomainParserHelper::getProcessedDomainInformation($rawUrl);

                                if ($domainInfo) {
                                    $allParsedLeads[] = $domainInfo;
                                    $totalLeadsCollected++;
                                } else {
                                    Log::warning('[FetchAdtractionJob] Could not parse domain info from programURL. Skipping.', ['programURL' => $programUrl]);
                                }

                            } else {
                                Log::warning('[FetchAdtractionJob] Missing programId or programURL in program data for market '.$countryCode.'. Skipping.', ['program_data_sample' => $program]);
                            }
                        }
                    } else {
                        Log::warning("[FetchAdtractionJob] No programs found or invalid format for market {$countryCode}");
                    }
                } else {
                    Log::warning('[FetchAdtractionJob] Market code not found in market data item.', ['market_item' => $market]);
                }
            }
        } else {
            Log::warning('[FetchAdtractionJob] No markets found or invalid format from Adtraction Markets API.');
        }

        if (! empty($allParsedLeads)) {
            Log::info('[FetchAdtractionJob] Passing '.count($allParsedLeads).' collected leads to batch processor.');
            $batchProcessor = new LeadBatchProcessorHelper;
            $batchProcessor->processLeads($allParsedLeads);
        } else {
            Log::info('[FetchAdtractionJob] No leads collected to process.');
        }

        Log::info("[FetchAdtractionJob] Job finished. Total leads prepared for batching: {$totalLeadsCollected}");
    }
}
