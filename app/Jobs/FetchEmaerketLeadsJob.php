<?php

namespace App\Jobs;

use Algolia\AlgoliaSearch\Api\SearchClient;
use App\Helpers\DomainParserHelper;
use App\Helpers\LeadBatchProcessorHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class FetchEmaerketLeadsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('[FetchEmaerketLeadsJob] Starting job.');
        $client = SearchClient::create('ZB0W34S4PD', '3ce0b18b379d822f3a00d96cb020edb1');
        $maxPage = 99; // Initial maxPage, will be updated from Algolia response
        $page = 0;
        $allParsedLeads = []; // Initialize array
        $totalLeadsCollected = 0;

        while ($page <= $maxPage) {
            try {
                $response = $client->search([
                    'requests' => [
                        [
                            'indexName' => 'Webshops',
                            'query' => '',
                            'hitsPerPage' => 500,
                            'page' => $page,
                        ],
                    ],
                ]);

                // Get the search results
                $searchResults = $response['results'][0];

                if ($page === 0 && isset($searchResults['nbPages'])) {
                    $maxPage = $searchResults['nbPages'] - 1; // nbPages is total, so maxPage is nbPages - 1
                }

                foreach ($searchResults['hits'] as $hit) {
                    if (empty(trim($hit['fullUrl']))) {
                        Log::warning('[FetchEmaerketLeadsJob] Skipping hit due to empty or whitespace fullUrl.');

                        continue;
                    }

                    $rawUrl = trim($hit['fullUrl']);
                    $domainInfo = DomainParserHelper::getProcessedDomainInformation($rawUrl);

                    if ($domainInfo) {
                        $allParsedLeads[] = $domainInfo;
                        $totalLeadsCollected++;
                    } else {
                        Log::warning('[FetchEmaerketLeadsJob] Could not parse domain info from fullUrl. Skipping.', ['fullUrl' => $rawUrl]);
                    }
                }

            } catch (\Algolia\AlgoliaSearch\Exceptions\AlgoliaUnreachableHostException $e) {
                Log::error('[FetchEmaerketLeadsJob] Algolia API unreachable: '.$e->getMessage());
                // Depending on policy, might break or retry after a delay
                break; // Stop processing if Algolia is down
            } catch (\Exception $e) {
                Log::error('[FetchEmaerketLeadsJob] Error fetching or processing data for page '.$page.': '.$e->getMessage());
                // Continue to next page if one page fails, or add specific error handling
            }

            $page++;
        }

        // Finalize processing for all collected leads
        if (! empty($allParsedLeads)) {
            Log::info('[FetchEmaerketLeadsJob] Passing '.count($allParsedLeads).' collected leads to batch processor.');
            $batchProcessor = new LeadBatchProcessorHelper;
            $batchProcessor->processLeads($allParsedLeads);
        } else {
            Log::info('[FetchEmaerketLeadsJob] No leads collected to process.');
        }

        Log::info("[FetchEmaerketLeadsJob] Job finished. Total leads prepared for batching: {$totalLeadsCollected}");
    }
}
