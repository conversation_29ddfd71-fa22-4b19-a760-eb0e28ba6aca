<?php

namespace App\Jobs;

use App\Helpers\DomainParserHelper;
use App\Helpers\LeadBatchProcessorHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FetchMollyAppJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct() {}

    public function handle(): void
    {
        Log::info('[FetchMollyAppJob] Starting job.');
        $allParsedLeads = [];
        $totalLeadsCollected = 0;

        $countries = [
            'dk',
            'se',
            'no',
            'de',
            'uk',
            'nl',
        ];
        $baseApiUrl = 'https://api.molly.gratis/publicsiteinfo';

        foreach ($countries as $country) {
            $apiUrl = "$baseApiUrl/$country/?limit=999999";
            Log::info("[FetchMollyAppJob] Processing country: {$country}, URL: {$apiUrl}");

            try {
                $response = Http::timeout(300)->get($apiUrl);
            } catch (\Exception $e) {
                Log::error('[FetchMollyAppJob] An error occurred while fetching site info for country '.$country, ['message' => $e->getMessage()]);

                continue;
            }

            if ($response->failed()) {
                Log::error('[FetchMollyAppJob] Failed to fetch site info for country '.$country.': '.$response->status());

                continue;
            }

            $sites = $response->json();

            if (is_array($sites)) {
                foreach ($sites as $site) {
                    if (isset($site['domain']) && ! empty(trim($site['domain']))) {
                        $rawDomain = trim($site['domain']);
                        $domainInfo = DomainParserHelper::getProcessedDomainInformation($rawDomain);

                        if ($domainInfo) {
                            $allParsedLeads[] = $domainInfo;
                            $totalLeadsCollected++;
                        } else {
                            Log::warning('[FetchMollyAppJob] Could not parse domain info from site domain. Skipping.', ['site_domain' => $rawDomain, 'country' => $country]);
                        }
                    } else {
                        Log::warning('[FetchMollyAppJob] Missing or empty domain in site info data for country '.$country.'. Skipping.', ['site_data_sample' => $site]);
                    }
                }
            } else {
                Log::info("[FetchMollyAppJob] No sites found or invalid format for country {$country}. Response: ".json_encode($sites));
            }
        }

        if (! empty($allParsedLeads)) {
            Log::info('[FetchMollyAppJob] Passing '.count($allParsedLeads).' collected leads to batch processor.');
            $batchProcessor = new LeadBatchProcessorHelper;
            $batchProcessor->processLeads($allParsedLeads);
        } else {
            Log::info('[FetchMollyAppJob] No leads collected to process.');
        }

        Log::info("[FetchMollyAppJob] Job finished. Total leads prepared for batching: {$totalLeadsCollected}");
    }
}
