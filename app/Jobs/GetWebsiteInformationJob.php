<?php

namespace App\Jobs;

use App\Helpers\WebsiteCompanyHelper;
use App\Helpers\WebsitePlatformInformationHelper;
use App\Models\Lead;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Throwable;

class GetWebsiteInformationJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;

    public int $backoff = 60;

    public int $leadId;

    protected ?string $url = null;

    public function __construct(int $leadId)
    {
        $this->leadId = $leadId;
    }

    public function uniqueId(): string
    {
        return 'website_information_'.$this->leadId;
    }

    public function handle(): void
    {
        Log::info('[GetWebsiteInformationJob] Processing lead ID: '.$this->leadId);
        $lead = Lead::find($this->leadId);

        if (! $lead) {
            Log::error('[GetWebsiteInformationJob] Lead not found with ID: '.$this->leadId);

            return;
        }

        $this->url = Str::startsWith($lead->domain, ['http://', 'https://']) ? $lead->domain : 'http://'.$lead->domain;

        if (empty($this->url) || $this->url === 'http://') {
            Log::error('[GetWebsiteInformationJob] Domain is empty or invalid for lead ID: '.$this->leadId);

            return;
        }

        try {
            $response = Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Encoding' => 'gzip, deflate, br',
                'Accept-Language' => 'da;q=0.8',
                'Referer' => 'https://www.google.com/',
            ])->timeout(15)->get($this->url);

            $response->throw();

            $htmlContent = substr(trim($response->body()), 0, 500000);  // Limit content to prevent excessive resource use

            if (empty($htmlContent)) {
                Log::warning("[GetWebsiteInformationJob] Empty HTML content for lead ID: {$this->leadId}, URL: {$this->url}");
                throw new \Exception('Empty HTML content received.');
            }

            // Get website information (will be used later on)
            $headers = $response->headers();                // Get the headers
            $cookies = $response->cookies()->toArray();     // Get the cookies (if available)
            $robotsTxt = $this->fetchRobotsTxt($this->url); // Fetch robots.txt

            // Get the IP address from the response headers
            $ipAddress = null;
            $parsedUrl = parse_url($this->url);
            $host = $parsedUrl['host'] ?? null;
            if ($host) {
                $ipAddress = gethostbyname($host);
            }

            // Prepare data to update
            $dataToUpdate = [];

            // Get ip address
            if ($ipAddress) {
                $dataToUpdate['website_ip'] = $ipAddress;
            }

            // Get website information
            if ($title = WebsiteCompanyHelper::extractTitle($htmlContent)) {
                $dataToUpdate['website_title'] = Str::limit($title, 250);
            }
            if ($metaDescription = WebsiteCompanyHelper::extractMetaDescription($htmlContent)) {
                $dataToUpdate['website_meta_description'] = $metaDescription;
            }
            if ($hreflangs = WebsiteCompanyHelper::extractHreflangs($htmlContent)) {
                $dataToUpdate['website_hreflang'] = Str::limit($hreflangs, 250);
            }
            if ($phoneNumber = WebsiteCompanyHelper::extractPhoneNumber($htmlContent)) {
                $dataToUpdate['website_phone'] = Str::limit($phoneNumber, 50);
            }
            if ($emailAddress = WebsiteCompanyHelper::extractEmailAddress($htmlContent)) {
                $dataToUpdate['website_email'] = Str::limit($emailAddress, 190);
            }

            // Get website footer information
            $contextTextForExtraction = WebsiteCompanyHelper::getContextTextForExtraction($htmlContent);
            if ($poweredBy = WebsiteCompanyHelper::extractPoweredBy($contextTextForExtraction)) {
                $dataToUpdate['website_powered_by'] = Str::limit($poweredBy, 190);
            }
            if ($vatNumber = WebsiteCompanyHelper::extractVatNumber($contextTextForExtraction)) {
                $dataToUpdate['website_vat'] = Str::limit($vatNumber, 50);
            }

            // Get website platform information
            $websitePlatformInformation = WebsitePlatformInformationHelper::getWebsitePlatformInformation($ipAddress, $htmlContent);

            if (! empty($websitePlatformInformation['platform_name'])) {
                $dataToUpdate['website_platform_name'] = Str::limit($websitePlatformInformation['platform_name']);
            }
            if (! empty($websitePlatformInformation['platform_version'])) {
                $dataToUpdate['website_platform_version'] = Str::limit($websitePlatformInformation['platform_version'], 50);
            }
            if (! empty($websitePlatformInformation['theme_name'])) {
                $dataToUpdate['website_theme_name'] = Str::limit($websitePlatformInformation['theme_name']);
            }
            if (! empty($websitePlatformInformation['theme_version'])) {
                $dataToUpdate['website_theme_version'] = Str::limit($websitePlatformInformation['theme_version'], 50);
            }

            // Extract "made by" information from context text and theme name
            $themeName = $websitePlatformInformation['theme_name'] ?? null;
            if ($madeBy = WebsiteCompanyHelper::extractMadeBy($contextTextForExtraction, $themeName)) {
                $dataToUpdate['website_made_by'] = Str::limit($madeBy, 190);
            }

            $lead->update($dataToUpdate);
            Log::info("[GetWebsiteInformationJob] Successfully processed and updated lead ID: {$this->leadId}");

        } catch (ConnectionException $e) {
            $errorMessage = "Connection error for lead ID {$this->leadId}, URL: {$this->url} - ".$e->getMessage();
            Log::error("[GetWebsiteInformationJob] {$errorMessage}");
            if ($this->attempts() < $this->tries) {
                $this->release($this->backoff * $this->attempts());
                Log::info("[GetWebsiteInformationJob] Releasing job for lead ID {$this->leadId} (attempt {$this->attempts()}) due to ConnectionException. Will retry.");
            } else {
                Log::error("[GetWebsiteInformationJob] Job for lead ID {$this->leadId} failed after max ({$this->tries}) tries due to ConnectionException. Final error: ".$e->getMessage());
                throw $e;
            }
        } catch (RequestException $e) {
            $statusCode = $e->response ? $e->response->status() : 'N/A';
            $errorMessage = "HTTP request failed (Status: {$statusCode}) for lead ID {$this->leadId}, URL: {$this->url} - ".$e->getMessage();
            Log::error("[GetWebsiteInformationJob] {$errorMessage}");
            if ($this->attempts() < $this->tries) {
                $this->release($this->backoff * $this->attempts());
                Log::info("[GetWebsiteInformationJob] Releasing job for lead ID {$this->leadId} (attempt {$this->attempts()}) due to RequestException. Will retry.");
            } else {
                Log::error("[GetWebsiteInformationJob] Job for lead ID {$this->leadId} failed after max ({$this->tries}) tries due to RequestException. Final error: ".$e->getMessage());
                throw $e;
            }
        } catch (Throwable $e) {
            $errorMessage = "General error processing lead ID {$this->leadId}, URL: {$this->url} - ".$e->getMessage();
            Log::error("[GetWebsiteInformationJob] {$errorMessage} Stack: ".$e->getTraceAsString());
            if ($this->attempts() < $this->tries) {
                $this->release($this->backoff * $this->attempts());
                Log::info("[GetWebsiteInformationJob] Releasing job for lead ID {$this->leadId} (attempt {$this->attempts()}) due to general Throwable. Will retry.");
            } else {
                Log::error("[GetWebsiteInformationJob] Job for lead ID {$this->leadId} failed after max ({$this->tries}) tries due to general Throwable. Final error: ".$e->getMessage());
                throw $e;
            }
        }
    }

    private function fetchRobotsTxt($url)
    {
        // Build the robots.txt URL
        $robotsUrl = rtrim($url, '/').'/robots.txt';

        // Perform the HTTP request to fetch robots.txt
        $response = Http::withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Encoding' => 'gzip, deflate, br',
            'Accept-Language' => 'da;q=0.8',
            'Referer' => 'https://www.google.com/',
        ])->timeout(5)->get($robotsUrl);

        return substr($response->successful() ? $response->body() : '', 0, 10000);  // Limit robots.txt size
    }
}
