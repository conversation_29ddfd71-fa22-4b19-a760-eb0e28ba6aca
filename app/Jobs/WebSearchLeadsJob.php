<?php

namespace App\Jobs;

use App\Helpers\DomainParserHelper;
use App\Helpers\LeadBatchProcessorHelper;
use App\Helpers\WebSearch\BraveSearchHelper;
use App\Helpers\WebSearch\BrightDataSerpHelper;
use App\Helpers\WebSearch\SerpApiHelper;
use App\Helpers\WebSearch\SerpStackHelper;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class WebSearchLeadsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The search engines to use.
     *
     * @var array
     */
    protected array $searchEngines;

    // No longer using countryCodes property - each engine uses its own country codes

    /**
     * Create a new job instance.
     *
     * @param array $searchEngines The search engines to use (supported: 'brave', 'bing', 'google')
     *                             Note:
     *                             - 'bing' can use either SerpStackHelper or BrightDataSerpHelper (if configured)
     *                             - 'google' can use either SerpApiHelper or BrightDataSerpHelper (if configured)
     *                             - 'brave' uses BraveSearchHelper
     *                             All search engines are configured to return 100 results and only results from the current day.
     */
    public function __construct(
        array $searchEngines = ['brave', 'bing', 'google']
    ) {
        $this->searchEngines = $searchEngines;
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        $environment = app()->environment();
        Log::info('[WebSearchLeadsJob] Starting job.', [
            'search_engines' => $this->searchEngines,
            'environment' => $environment,
            'test_mode' => ($environment !== 'production'),
        ]);

        $allParsedLeads = [];
        $totalLeadsCollected = 0;

        // Initialize search helpers with all configuration
        $searchHelpers = $this->initializeSearchHelpers();
        if (empty($searchHelpers)) {
            Log::error('[WebSearchLeadsJob] No search helpers could be initialized. Aborting job.');
            return;
        }

        // Process each search engine with its appropriate country codes
        foreach ($searchHelpers as $engineName => $helperConfig) {
            // Get the helper class, country codes and search parameters
            $helper = $helperConfig['class'];
            $helperType = $helperConfig['type'];
            $countryCodes = $helperConfig['country_codes'];
            $searchParams = $helperConfig['search_params'];

            Log::info("[WebSearchLeadsJob] Processing {$engineName} search with " . count($countryCodes) . " country codes");

            // Process each country code for this engine
            foreach ($countryCodes as $countryCode) {
                // Build the query
                $query = 'inurl:"' . $countryCode . '/collections/all/" -inurl:"shopify" -inurl:"stackoverflow" -inurl:"&" -inurl:"?" -inurl:"?page" -inurl:"?srsltid"';

                Log::info("[WebSearchLeadsJob] Processing {$engineName} search for country code: {$countryCode} with query: {$query}");

                // Prepare search parameters with the actual country code
                $params = $this->prepareSearchParams($searchParams, $countryCode);

                // Perform the search
                try {
                    // Perform the search based on the helper type
                    $results = null;

                    if ($helperType === 'brave') {
                        $results = $helper->search($query, $params);
                    } elseif ($helperType === 'brightdata') {
                        $results = $helper->search($engineName, $query, strtoupper($countryCode), $params);
                    } elseif ($helperType === 'serpstack') {
                        $results = $helper->search($query, $params);
                    } elseif ($helperType === 'serpapi') {
                        $results = $helper->search('google', $query, strtoupper($countryCode), $params);
                    }

                    if (!empty($results)) {
                        // Process the results and extract domain information
                        $parsedResults = $this->processSearchResults($results, $engineName);

                        if (!empty($parsedResults)) {
                            $allParsedLeads = array_merge($allParsedLeads, $parsedResults);
                            $totalLeadsCollected += count($parsedResults);

                            Log::info("[WebSearchLeadsJob] Collected {$totalLeadsCollected} leads so far from {$engineName} for country code: {$countryCode}");
                        }
                    }
                } catch (Exception $e) {
                    Log::error("[WebSearchLeadsJob] Error performing search with {$engineName} for country code {$countryCode}: " . $e->getMessage());
                }

                // Add a small delay between requests to avoid rate limiting
                sleep(1);
            }
        }

        // Process all collected leads
        if (!empty($allParsedLeads)) {
            Log::info('[WebSearchLeadsJob] Passing ' . count($allParsedLeads) . ' collected leads to batch processor.');
            $batchProcessor = new LeadBatchProcessorHelper;
            $batchProcessor->processLeads($allParsedLeads);
        } else {
            Log::info('[WebSearchLeadsJob] No leads collected to process.');
        }

        Log::info("[WebSearchLeadsJob] Job finished. Total leads prepared for batching: {$totalLeadsCollected}");
    }

    /**
     * Initialize the search helpers for each enabled search engine.
     * This method also configures country codes and search parameters for each engine.
     *
     * @return array The initialized search helpers with configuration
     * @throws Exception
     */
    protected function initializeSearchHelpers(): array
    {
        $helpers = [];

        // Get API keys and configurations
        $brightDataConfig = [
            'api_key' => config('crawler.brightdata_api_key'),
            'customer_id' => config('crawler.brightdata_customer_id'),
            'zone_id' => config('crawler.brightdata_zone_id')
        ];

        $braveApiKey = config('crawler.brave_search_api_key');
        $serpApiKey = config('crawler.serpapi_api_key');
        $serpStackApiKey = config('crawler.serpstack_api_key');

        // Check if BrightData is configured
        $brightDataConfigured = !empty($brightDataConfig['api_key']) &&
                               !empty($brightDataConfig['customer_id']) &&
                               !empty($brightDataConfig['zone_id']);

        // Create BrightData helper if configured (will be reused for both Google and Bing)
        $brightDataHelper = null;
        if ($brightDataConfigured) {
            $brightDataHelper = new BrightDataSerpHelper(
                $brightDataConfig['api_key'],
                $brightDataConfig['customer_id'],
                $brightDataConfig['zone_id']
            );
            Log::info('[WebSearchLeadsJob] Initialized BrightDataSerpHelper');
        }

        // Common TLDs and special domains to include for all engines
        $commonTlds = ['com', 'eu', 'nu', 'store', 'net', 'online', 'org', 'info', 'biz', 'site', 'website', 'tech', 'app', 'blog'];

        // Initialize helpers for each requested search engine
        foreach ($this->searchEngines as $engine) {
            try {
                $engineName = strtolower($engine);
                $helperClass = null;
                $helperType = '';
                $countryCodes = [];

                // Define search parameters for this engine
                $searchParams = [];

                switch ($engineName) {
                    case 'brave':
                        if (!empty($braveApiKey)) {
                            $helperClass = new BraveSearchHelper($braveApiKey);
                            $helperType = 'brave';

                            // Get country codes
                            $countryCodes = array_map('strtolower', BraveSearchHelper::SUPPORTED_COUNTRY_CODES);

                            // Process country codes
                            // Merge with common TLDs
                            $countryCodes = array_merge($countryCodes, $commonTlds);

                            // Remove duplicates and sort
                            $countryCodes = array_unique($countryCodes);
                            sort($countryCodes);

                            // If not in production environment, only use one country code for testing
                            if (app()->environment() !== 'production') {
                                Log::info("[WebSearchLeadsJob] Non-production environment detected. Using only one country code for {$engineName}.");
                                $countryCodes = ['dk']; // Use Denmark as the test country
                            }

                            $searchParams = [
                                'country' => '{COUNTRY_CODE}', // Will be replaced with actual country code
                                'count' => 100,
                                'freshness' => 'pd:1', // Past day (current day only)
                            ];

                            Log::info('[WebSearchLeadsJob] Initialized BraveSearchHelper');
                        } else {
                            Log::warning('[WebSearchLeadsJob] Brave Search API key not configured. Skipping Brave search.');
                            continue; // Skip to next engine
                        }
                        break;

                    case 'bing':
                        if ($brightDataConfigured) {
                            $helperClass = $brightDataHelper;
                            $helperType = 'brightdata';

                            // Get country codes
                            $countryCodes = array_map('strtolower', BrightDataSerpHelper::SUPPORTED_COUNTRY_CODES);

                            // Process country codes
                            // Merge with common TLDs
                            $countryCodes = array_merge($countryCodes, $commonTlds);

                            // Remove duplicates and sort
                            $countryCodes = array_unique($countryCodes);
                            sort($countryCodes);

                            // If not in production environment, only use one country code for testing
                            if (app()->environment() !== 'production') {
                                Log::info("[WebSearchLeadsJob] Non-production environment detected. Using only one country code for {$engineName}.");
                                $countryCodes = ['dk']; // Use Denmark as the test country
                            }

                            $searchParams = [
                                'engine' => 'bing',
                                'country_code' => '{COUNTRY_CODE}', // Will be replaced with actual country code
                                'count' => 100,
                                'time' => 'day', // Current day only
                                'first' => 1,    // First page of results
                                'mkt' => 'en-US', // English US market
                            ];

                            Log::info('[WebSearchLeadsJob] Using BrightDataSerpHelper for Bing');
                        } elseif (!empty($serpStackApiKey)) {
                            $helperClass = new SerpStackHelper($serpStackApiKey, true);
                            $helperType = 'serpstack';

                            // Get country codes
                            $countryCodes = array_map('strtolower', SerpStackHelper::SUPPORTED_COUNTRY_CODES);

                            // Process country codes
                            // Merge with common TLDs
                            $countryCodes = array_merge($countryCodes, $commonTlds);

                            // Remove duplicates and sort
                            $countryCodes = array_unique($countryCodes);
                            sort($countryCodes);

                            // If not in production environment, only use one country code for testing
                            if (app()->environment() !== 'production') {
                                Log::info("[WebSearchLeadsJob] Non-production environment detected. Using only one country code for {$engineName}.");
                                $countryCodes = ['dk']; // Use Denmark as the test country
                            }

                            $searchParams = [
                                'engine' => 'bing',
                                'country_code' => '{COUNTRY_CODE}', // Will be replaced with actual country code
                                'num' => 100,
                                'time' => 'day', // Current day only
                            ];

                            Log::info('[WebSearchLeadsJob] Using SerpStackHelper for Bing');
                        } else {
                            Log::warning('[WebSearchLeadsJob] No API keys configured for Bing search. Skipping.');
                            continue; // Skip to next engine
                        }
                        break;

                    case 'google':
                        if ($brightDataConfigured) {
                            $helperClass = $brightDataHelper;
                            $helperType = 'brightdata';

                            // Get country codes
                            $countryCodes = array_map('strtolower', BrightDataSerpHelper::SUPPORTED_COUNTRY_CODES);

                            // Process country codes
                            // Merge with common TLDs
                            $countryCodes = array_merge($countryCodes, $commonTlds);

                            // Remove duplicates and sort
                            $countryCodes = array_unique($countryCodes);
                            sort($countryCodes);

                            // If not in production environment, only use one country code for testing
                            if (app()->environment() !== 'production') {
                                Log::info("[WebSearchLeadsJob] Non-production environment detected. Using only one country code for {$engineName}.");
                                $countryCodes = ['dk']; // Use Denmark as the test country
                            }

                            $searchParams = [
                                'gl' => '{COUNTRY_CODE}', // Country code for Google (e.g., 'dk', 'us')
                                'num' => 100,
                                'tbs' => 'qdr:d', // Current day only
                                'hl' => 'en',     // English language
                                'start' => 0,     // First page of results
                                'brd_mobile' => 0, // Desktop user agent
                            ];

                            Log::info('[WebSearchLeadsJob] Using BrightDataSerpHelper for Google');
                        } elseif (!empty($serpApiKey)) {
                            $helperClass = new SerpApiHelper($serpApiKey);
                            $helperType = 'serpapi';

                            // Get country codes
                            $countryCodes = array_map('strtolower', SerpApiHelper::SUPPORTED_COUNTRY_CODES);

                            // Process country codes
                            // Merge with common TLDs
                            $countryCodes = array_merge($countryCodes, $commonTlds);

                            // Remove duplicates and sort
                            $countryCodes = array_unique($countryCodes);
                            sort($countryCodes);

                            // If not in production environment, only use one country code for testing
                            if (app()->environment() !== 'production') {
                                Log::info("[WebSearchLeadsJob] Non-production environment detected. Using only one country code for {$engineName}.");
                                $countryCodes = ['dk']; // Use Denmark as the test country
                            }

                            $searchParams = [
                                'gl' => '{COUNTRY_CODE}', // Country code for Google
                                'country_code' => '{COUNTRY_CODE}', // For SerpApiHelper
                                'num' => 100,
                                'tbs' => 'qdr:d', // Current day only
                            ];

                            Log::info('[WebSearchLeadsJob] Using SerpApiHelper for Google');
                        } else {
                            Log::warning('[WebSearchLeadsJob] No API keys configured for Google search. Skipping.');
                            continue; // Skip to next engine
                        }
                        break;

                    default:
                        Log::warning("[WebSearchLeadsJob] Unsupported search engine: {$engineName}. Skipping.");
                        continue; // Skip to next engine
                }

                // Add the helper to the helpers array with all configuration in one place
                $helpers[$engineName] = [
                    'class' => $helperClass,
                    'type' => $helperType,
                    'engine' => $engineName,
                    'country_codes' => $countryCodes,
                    'search_params' => $searchParams
                ];

            } catch (Exception $e) {
                Log::error("[WebSearchLeadsJob] Error initializing search helper for {$engine}: " . $e->getMessage());
            }
        }

        return $helpers;
    }

    /**
     * Prepare search parameters by replacing placeholders with actual values.
     *
     * @param array $params The search parameters with placeholders
     * @param string $countryCode The country code to use
     * @return array The prepared search parameters
     */
    protected function prepareSearchParams(array $params, string $countryCode): array
    {
        $result = [];

        foreach ($params as $key => $value) {
            if (is_string($value) && $value === '{COUNTRY_CODE}') {
                $result[$key] = strtoupper($countryCode);
            } else {
                $result[$key] = $value;
            }
        }

        return $result;
    }



    /**
     * Process the search results and extract domain information.
     *
     * @param array $results The search results
     * @param string $engineName The name of the search engine
     * @return array The processed results with domain information
     */
    protected function processSearchResults(array $results, string $engineName): array
    {
        $processedResults = [];

        try {
            $organicResults = $this->extractOrganicResults($results, $engineName);

            if (empty($organicResults)) {
                Log::info("[WebSearchLeadsJob] No organic results found for {$engineName}.");
                return [];
            }

            foreach ($organicResults as $result) {
                $url = $this->extractUrlFromResult($result, $engineName);

                if (empty(trim($url))) {
                    continue;
                }

                $domainInfo = DomainParserHelper::getProcessedDomainInformation($url);

                if ($domainInfo) {
                    $processedResults[] = $domainInfo;
                }
            }

            Log::info("[WebSearchLeadsJob] Processed " . count($processedResults) . " results from {$engineName}.");

        } catch (Exception $e) {
            Log::error("[WebSearchLeadsJob] Error processing search results from {$engineName}: " . $e->getMessage());
        }

        return $processedResults;
    }

    /**
     * Extract organic results from the search results based on the search engine.
     *
     * @param array $results The search results
     * @param string $engineName The name of the search engine
     * @return array The organic results
     */
    protected function extractOrganicResults(array $results, string $engineName): array
    {
        switch ($engineName) {
            case 'brave':
                return $results['web']['results'] ?? [];

            case 'bing':
            case 'google':
                return $results['organic_results'] ?? [];

            default:
                return [];
        }
    }

    /**
     * Extract the URL from a search result based on the search engine.
     *
     * @param array $result The search result
     * @param string $engineName The name of the search engine
     * @return string The extracted URL
     */
    protected function extractUrlFromResult(array $result, string $engineName): string
    {
        switch ($engineName) {
            case 'brave':
                return $result['url'] ?? '';

            case 'bing':
            case 'google':
                return $result['link'] ?? '';

            default:
                return '';
        }
    }


}
