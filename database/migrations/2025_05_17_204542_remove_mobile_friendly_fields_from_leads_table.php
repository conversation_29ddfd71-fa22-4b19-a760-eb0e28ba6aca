<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->dropColumn([
                'google_mobile_friendly_score',
                'google_mobile_friendly_pass',
                'google_mobile_friendly_screenshot',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Assuming original types. Adjust if they were different.
            // Positioning them back might be tricky if other columns were added/removed around them.
            // For simplicity, adding them at the end. Consider their original position if critical.
            $table->integer('google_mobile_friendly_score')->nullable();
            $table->boolean('google_mobile_friendly_pass')->nullable();
            $table->text('google_mobile_friendly_screenshot')->nullable();
        });
    }
};
