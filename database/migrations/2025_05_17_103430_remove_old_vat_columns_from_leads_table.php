<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->dropColumn([
                'vat_name',
                'vat_address',
                'vat_addressco',
                'vat_zipcode',
                'vat_protected',
                'vat_phone',
                'vat_email',
                'vat_startdate',
                'vat_enddate',
                'vat_employees',
                'vat_industrycode',
                'vat_companycode',
                'vat_owners',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->string('vat_name', 255)->nullable();
            $table->string('vat_address', 255)->nullable();
            $table->string('vat_addressco', 255)->nullable();
            $table->string('vat_zipcode', 255)->nullable();
            $table->integer('vat_protected')->nullable();
            $table->string('vat_phone', 255)->nullable();
            $table->string('vat_email', 255)->nullable();
            $table->timestamp('vat_startdate')->nullable(); // Consider original default if important
            $table->timestamp('vat_enddate')->nullable();   // Consider original default if important
            $table->string('vat_employees', 255)->nullable();
            $table->text('vat_industrycode')->nullable();
            $table->string('vat_companycode', 255)->nullable();
            $table->text('vat_owners')->nullable();
        });
    }
};
