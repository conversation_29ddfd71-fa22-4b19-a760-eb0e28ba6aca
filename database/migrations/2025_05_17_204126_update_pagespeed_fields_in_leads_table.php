<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // ADD 'google_pagespeed_desktop_performance_score' as '..._speed_score' might not exist
            $table->integer('google_pagespeed_desktop_performance_score')->nullable()->after('comment'); // Assuming 'comment' field exists and this is a good place
            // ADD 'google_pagespeed_desktop_accessibility_score' as it didn't exist as '..._usability_score'
            $table->integer('google_pagespeed_desktop_accessibility_score')->nullable()->after('google_pagespeed_desktop_performance_score');

            // Add new desktop columns
            $table->integer('google_pagespeed_desktop_seo_score')->nullable()->after('google_pagespeed_desktop_accessibility_score');
            $table->integer('google_pagespeed_desktop_best_practices_score')->nullable()->after('google_pagespeed_desktop_seo_score');

            // Rename existing mobile columns
            $table->renameColumn('google_pagespeed_mobile_speed_score', 'google_pagespeed_mobile_performance_score');
            $table->renameColumn('google_pagespeed_mobile_usability_score', 'google_pagespeed_mobile_accessibility_score');

            // Add new mobile columns
            $table->integer('google_pagespeed_mobile_seo_score')->nullable()->after('google_pagespeed_mobile_accessibility_score');
            $table->integer('google_pagespeed_mobile_best_practices_score')->nullable()->after('google_pagespeed_mobile_seo_score');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Drop the added 'google_pagespeed_desktop_performance_score' and other new desktop columns
            $table->dropColumn([
                'google_pagespeed_desktop_performance_score',
                'google_pagespeed_desktop_accessibility_score',
                'google_pagespeed_desktop_seo_score',
                'google_pagespeed_desktop_best_practices_score',
            ]);

            // Revert renaming of mobile columns
            $table->renameColumn('google_pagespeed_mobile_performance_score', 'google_pagespeed_mobile_speed_score');
            $table->renameColumn('google_pagespeed_mobile_accessibility_score', 'google_pagespeed_mobile_usability_score');

            // Drop new mobile columns
            $table->dropColumn(['google_pagespeed_mobile_seo_score', 'google_pagespeed_mobile_best_practices_score']);
        });
    }
};
