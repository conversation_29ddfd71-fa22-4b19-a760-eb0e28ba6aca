<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Find domains with more than one entry
        $duplicateDomains = DB::table('leads')
            ->select('domain', DB::raw('COUNT(*) as count'))
            ->whereNotNull('domain') // Ensure we only process non-null domains
            ->where('domain', '!=', '') // Ensure we only process non-empty domains
            ->groupBy('domain')
            ->havingRaw('COUNT(*) > 1')
            ->get();

        foreach ($duplicateDomains as $duplicate) {
            // Get all IDs for this duplicate domain, ordered by ID (oldest first)
            $leadIds = DB::table('leads')
                ->where('domain', $duplicate->domain)
                ->orderBy('id', 'asc')
                ->pluck('id')
                ->toArray();

            // The first ID in the sorted list is the one to keep
            array_shift($leadIds); // Remove the ID to keep from the list of IDs to delete

            // If there are any remaining IDs, they are duplicates to be deleted
            if (! empty($leadIds)) {
                DB::table('leads')->whereIn('id', $leadIds)->delete();
            }
        }

        // Now that duplicates are removed, modify the table
        Schema::table('leads', function (Blueprint $table) {
            // Drop the old simple index on 'domain'.
            // Providing the column name(s) to dropIndex is generally robust.
            $table->dropIndex(['domain']);

            // Add the unique constraint
            $table->unique('domain');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Drop the unique constraint
            // Providing the column name(s) to dropUnique is generally robust.
            $table->dropUnique(['domain']);

            // Re-add the simple index
            $table->index('domain');
        });
    }
};
