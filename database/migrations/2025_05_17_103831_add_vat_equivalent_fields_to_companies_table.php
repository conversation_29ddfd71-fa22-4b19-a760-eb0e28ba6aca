<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->integer('protected')->nullable()->after('status');
            $table->string('phone')->nullable()->after('city');
            $table->string('email')->nullable()->after('phone');
            $table->string('employees')->nullable()->after('email');
            $table->text('industry_code')->nullable()->after('employees');
            $table->string('company_code')->nullable()->after('industry_code');
            $table->text('owners')->nullable()->after('company_code');

            if (Schema::hasColumn('companies', 'start_date')) {
                $table->renameColumn('start_date', 'date_start');
                $table->timestamp('date_start')->nullable()->change(); // Change type of the renamed column
            }

            $table->timestamp('date_end')->nullable()->after('date_start'); // Position after the now date_start

            // Drop the 'type' column
            if (Schema::hasColumn('companies', 'type')) {
                $table->dropColumn('type');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            // Re-add the 'type' column first
            if (! Schema::hasColumn('companies', 'type')) {
                $table->string('type')->nullable()->after('status');
            }

            // Drop the added columns (including date_end)
            $table->dropColumn([
                'protected',
                'phone',
                'email',
                'employees',
                'industry_code',
                'company_code',
                'owners',
                'date_end', // This was newly added in up()
            ]);

            // Revert date_start to start_date (change type first, then rename)
            if (Schema::hasColumn('companies', 'date_start')) {
                $table->date('date_start')->nullable()->change(); // Change type back to date
                $table->renameColumn('date_start', 'start_date'); // Then rename
            }
        });
    }
};
