<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Step 1: Handle website_platform_name (rename or create)
            if (Schema::hasColumn('leads', 'website_platform') && ! Schema::hasColumn('leads', 'website_platform_name')) {
                $table->renameColumn('website_platform', 'website_platform_name');
            } elseif (! Schema::hasColumn('leads', 'website_platform_name')) {
                $afterColumnForPlatformName = Schema::hasColumn('leads', 'website_status') ? 'website_status' : null;
                if ($afterColumnForPlatformName) {
                    $table->string('website_platform_name')->nullable()->after($afterColumnForPlatformName);
                } else {
                    $table->string('website_platform_name')->nullable();
                }
            }

            // Step 2: Define other technology columns and add them if they don't exist.
            $technologyColumns = [
                'website_platform_version' => ['type' => 'string', 'after' => 'website_platform_name'],
                'website_theme_name' => ['type' => 'string', 'after' => 'website_platform_version'],
                'website_theme_version' => ['type' => 'string', 'after' => 'website_theme_name'],
                'website_technologies' => ['type' => 'jsonb',  'after' => 'website_theme_version'],
            ];

            foreach ($technologyColumns as $columnName => $config) {
                if (! Schema::hasColumn('leads', $columnName)) {
                    $columnDefinition = $table->{$config['type']}($columnName)->nullable();
                    // Ensure the 'after' column actually exists before trying to use it.
                    if (Schema::hasColumn('leads', $config['after'])) {
                        $columnDefinition->after($config['after']);
                    }
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $columnsToDropIfExist = [
                'website_technologies',
                'website_theme_version',
                'website_theme_name',
                'website_platform_version',
            ];

            $actualColumnsToDrop = [];
            foreach ($columnsToDropIfExist as $colName) {
                if (Schema::hasColumn('leads', $colName)) {
                    $actualColumnsToDrop[] = $colName;
                }
            }

            if (! empty($actualColumnsToDrop)) {
                $table->dropColumn($actualColumnsToDrop);
            }

            // Handle reverting/dropping website_platform_name
            if (Schema::hasColumn('leads', 'website_platform_name')) {
                if (! Schema::hasColumn('leads', 'website_platform')) {
                    // Original 'website_platform' does not exist, so rename 'website_platform_name' back to it.
                    $table->renameColumn('website_platform_name', 'website_platform');
                } else {
                    // Original 'website_platform' DOES exist. This means 'website_platform_name'
                    // was either a new column, or the rename target already existed.
                    // In this case, 'website_platform_name' should be dropped.
                    $table->dropColumn('website_platform_name');
                }
            }
        });
    }
};
