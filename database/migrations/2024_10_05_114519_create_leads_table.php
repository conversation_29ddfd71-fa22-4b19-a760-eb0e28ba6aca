<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leads', function (Blueprint $table) {
            $table->id();
            $table->timestamp('data_updated')->nullable()->useCurrent();
            $table->integer('platform')->nullable()->default(0);
            $table->boolean('ssl')->nullable()->default(0);
            $table->boolean('website_type')->nullable()->default(0);
            $table->string('website_url', 255)->nullable();
            $table->string('domain', 255)->nullable();
            $table->integer('domain_status')->nullable()->default(1);
            $table->timestamp('domain_created')->nullable();
            $table->timestamp('domain_expires')->nullable();
            $table->string('domain_owner', 50)->nullable()->default('');
            $table->text('comment')->nullable();
            $table->string('vat_id', 255)->nullable();
            $table->string('vat_name', 255)->nullable();
            $table->string('vat_address', 255)->nullable();
            $table->string('vat_addressco', 255)->nullable();
            $table->string('vat_zipcode', 255)->nullable();
            $table->integer('vat_protected')->nullable();
            $table->string('vat_phone', 255)->nullable();
            $table->string('vat_email', 255)->nullable();
            $table->timestamp('vat_startdate')->nullable()->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('vat_enddate')->nullable()->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->string('vat_employees', 255)->nullable();
            $table->text('vat_industrycode')->nullable();
            $table->string('vat_companycode', 255)->nullable();
            $table->text('vat_owners')->nullable();
            $table->tinyInteger('google_pagespeed_desktop_speed_score')->nullable();
            $table->longText('google_pagespeed_desktop_screenshot')->nullable();
            $table->tinyInteger('google_pagespeed_mobile_speed_score')->nullable();
            $table->tinyInteger('google_pagespeed_mobile_usability_score')->nullable();
            $table->longText('google_pagespeed_mobile_screenshot')->nullable();
            $table->tinyInteger('google_mobile_friendly_score')->nullable();
            $table->boolean('google_mobile_friendly_pass')->nullable();
            $table->longText('google_mobile_friendly_screenshot')->nullable();
            $table->boolean('contact')->nullable()->default(0);
            $table->boolean('relevant')->nullable()->default(1);
            $table->integer('manual')->nullable()->default(0);
            $table->boolean('deleted')->nullable()->default(0);
            $table->timestamp('change')->nullable()->useCurrent()->useCurrentOnUpdate();

            $table->date('created_at')->nullable()->useCurrent();
            $table->date('updated_at')->nullable()->useCurrent()->useCurrentOnUpdate();

            // Indexes
            $table->index('domain');
            $table->index('vat_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('leads');
    }
};
