<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Change domain_status to boolean and set default to 0 (false)
            $table->boolean('domain_status')->default(0)->change();
            // Change website_status to boolean and set default to 0 (false)
            $table->boolean('website_status')->default(0)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Revert domain_status to integer and set default to 1 (true)
            $table->integer('domain_status')->default(1)->change();
            // Revert website_status to tinyInteger and set default to 0 (false)
            $table->tinyInteger('website_status')->default(0)->change();
        });
    }
};
