<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            if (Schema::hasColumn('leads', 'ssl')) {
                $table->renameColumn('ssl', 'website_ssl');
            }
            if (Schema::hasColumn('leads', 'platform')) {
                $table->renameColumn('platform', 'website_platform');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            if (Schema::hasColumn('leads', 'website_ssl')) {
                $table->renameColumn('website_ssl', 'ssl');
            }
            if (Schema::hasColumn('leads', 'website_platform')) {
                $table->renameColumn('website_platform', 'platform');
            }
        });
    }
};
