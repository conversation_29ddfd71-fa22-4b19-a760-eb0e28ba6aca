<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Update existing 0 values to NULL before changing type
            DB::table('leads')
                ->where('website_platform_name', 0)
                ->update(['website_platform_name' => null]);

            // Change the column to a string type.
            // You might want to adjust the length (e.g., 255) as needed.
            // Adding ->nullable() if the column can be empty.
            $table->string('website_platform_name')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Revert to an integer type if needed.
            // Be cautious: if string data exists, this might cause data loss or errors.
            // Consider if you truly need to revert to integer or what the original type was.
            // If it was an enum-like integer, this would be the place to revert.
            // For now, assuming it was a generic integer.
            // If you changed 0 to NULL in up(), you might consider changing NULL back to 0 here
            // if 0 had a specific meaning (e.g., 'Unknown').
            // DB::table('leads')->whereNull('website_platform_name')->update(['website_platform_name' => 0]);
            $table->integer('website_platform_name')->nullable()->change();
        });
    }
};
