<?php

namespace Tests\Unit;

use App\Helpers\WebsiteCompanyHelper;
use PHPUnit\Framework\TestCase;

class WebsiteCompanyHelperMadeByTest extends TestCase
{
    public function test_extract_made_by_from_danish_text()
    {
        $contextText = 'Designet og udviklet af Emil fra Gelinde';
        $result = WebsiteCompanyHelper::extractMadeBy($contextText);
        $this->assertEquals('Gelinde', $result);
    }

    public function test_extract_made_by_from_english_text()
    {
        $contextText = 'Made by John from WebAgency';
        $result = WebsiteCompanyHelper::extractMadeBy($contextText);
        $this->assertEquals('WebAgency', $result);
    }

    public function test_extract_developer_from_shopify_theme_brackets()
    {
        $themeName = '[Mercive] Production - DK';
        $result = WebsiteCompanyHelper::extractDeveloperFromShopifyTheme($themeName);
        $this->assertEquals('Mercive', $result);
    }

    public function test_extract_developer_from_shopify_theme_x_pattern()
    {
        $themeName = 'Vitalbody x Webshopskolen';
        $result = WebsiteCompanyHelper::extractDeveloperFromShopifyTheme($themeName);
        $this->assertEquals('Webshopskolen', $result);
    }

    public function test_extract_made_by_with_theme_name_priority()
    {
        $contextText = 'Some random text';
        $themeName = 'Store x DeveloperName - Theme';
        $result = WebsiteCompanyHelper::extractMadeBy($contextText, $themeName);
        $this->assertEquals('DeveloperName', $result);
    }

    public function test_extract_made_by_fallback_to_context()
    {
        $contextText = 'Udviklet af TestCompany';
        $themeName = 'Regular Theme Name';
        $result = WebsiteCompanyHelper::extractMadeBy($contextText, $themeName);
        $this->assertEquals('TestCompany', $result);
    }

    public function test_extract_made_by_returns_null_for_no_match()
    {
        $contextText = 'No developer information here';
        $result = WebsiteCompanyHelper::extractMadeBy($contextText);
        $this->assertNull($result);
    }

    public function test_clean_extracted_name_removes_company_suffixes()
    {
        $themeName = '[TestCompany ApS] Theme Name';
        $result = WebsiteCompanyHelper::extractDeveloperFromShopifyTheme($themeName);
        $this->assertEquals('TestCompany', $result);
    }

    public function test_extract_developer_from_shopify_theme_by_pattern()
    {
        $themeName = 'Theme by DeveloperName';
        $result = WebsiteCompanyHelper::extractDeveloperFromShopifyTheme($themeName);
        $this->assertEquals('DeveloperName', $result);
    }

    public function test_extract_developer_from_shopify_theme_dash_pattern()
    {
        $themeName = 'DeveloperName - Custom Theme';
        $result = WebsiteCompanyHelper::extractDeveloperFromShopifyTheme($themeName);
        $this->assertEquals('DeveloperName', $result);
    }

    public function test_extract_developer_skips_theme_like_names()
    {
        $themeName = 'Theme Store - Custom Design';
        $result = WebsiteCompanyHelper::extractDeveloperFromShopifyTheme($themeName);
        $this->assertNull($result);
    }

    public function test_extract_made_by_skips_production_terms()
    {
        $themeName = '[Production] Live Site';
        $result = WebsiteCompanyHelper::extractDeveloperFromShopifyTheme($themeName);
        $this->assertNull($result);
    }
}
