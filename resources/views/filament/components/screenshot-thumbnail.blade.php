@if(!empty($getState()))
    @php
        $url = $getState();
    @endphp
    
    <div class="screenshot-thumbnail">
        @if(str_starts_with($url, 'data:image/'))
            {{-- Handle base64 encoded images --}}
            <img 
                src="{{ $url }}" 
                alt="Screenshot thumbnail" 
                class="w-16 h-12 object-cover rounded border border-gray-200 cursor-pointer hover:opacity-80 transition-opacity"
                onclick="openScreenshotModal('{{ $url }}', 'Screenshot')"
            />
        @elseif(filter_var($url, FILTER_VALIDATE_URL))
            {{-- Handle regular URLs --}}
            <img 
                src="{{ $url }}" 
                alt="Screenshot thumbnail" 
                class="w-16 h-12 object-cover rounded border border-gray-200 cursor-pointer hover:opacity-80 transition-opacity"
                onclick="openScreenshotModal('{{ $url }}', 'Screenshot')"
                onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
            />
            <div class="hidden w-16 h-12 bg-gray-100 rounded border border-gray-200 flex items-center justify-center">
                <span class="text-xs text-gray-400">No img</span>
            </div>
        @else
            {{-- Invalid URL --}}
            <div class="w-16 h-12 bg-gray-100 rounded border border-gray-200 flex items-center justify-center">
                <span class="text-xs text-gray-400">Invalid</span>
            </div>
        @endif
    </div>

    @once
        {{-- Modal for viewing full-size screenshots --}}
        <div id="screenshot-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4" onclick="closeScreenshotModal()">
            <div class="bg-white rounded-lg max-w-4xl max-h-full overflow-auto" onclick="event.stopPropagation()">
                <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 id="modal-title" class="text-lg font-semibold">Screenshot</h3>
                    <button onclick="closeScreenshotModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <img id="modal-image" src="" alt="" class="max-w-full h-auto" />
                </div>
            </div>
        </div>

        <script>
            function openScreenshotModal(url, title) {
                const modal = document.getElementById('screenshot-modal');
                const modalImage = document.getElementById('modal-image');
                const modalTitle = document.getElementById('modal-title');
                
                modalImage.src = url;
                modalImage.alt = title;
                modalTitle.textContent = title;
                modal.classList.remove('hidden');
                modal.classList.add('flex');
            }

            function closeScreenshotModal() {
                const modal = document.getElementById('screenshot-modal');
                modal.classList.add('hidden');
                modal.classList.remove('flex');
            }

            // Close modal on Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeScreenshotModal();
                }
            });
        </script>
    @endonce
@else
    <div class="w-16 h-12 bg-gray-50 rounded border border-gray-200 flex items-center justify-center">
        <span class="text-xs text-gray-400">No img</span>
    </div>
@endif
