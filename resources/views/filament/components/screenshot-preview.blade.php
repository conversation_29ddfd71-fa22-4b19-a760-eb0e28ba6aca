@if(!empty($url))
    <div class="screenshot-preview">
        @if(str_starts_with($url, 'data:image/'))
            {{-- Handle base64 encoded images --}}
            <img 
                src="{{ $url }}" 
                alt="{{ $alt ?? 'Screenshot' }}" 
                class="max-w-full h-auto rounded-lg border border-gray-200 shadow-sm"
                style="max-height: 400px; object-fit: contain;"
                loading="lazy"
            />
        @elseif(filter_var($url, FILTER_VALIDATE_URL))
            {{-- Handle regular URLs --}}
            <img 
                src="{{ $url }}" 
                alt="{{ $alt ?? 'Screenshot' }}" 
                class="max-w-full h-auto rounded-lg border border-gray-200 shadow-sm"
                style="max-height: 400px; object-fit: contain;"
                loading="lazy"
                onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
            />
            <div class="hidden text-sm text-gray-500 p-4 border border-gray-200 rounded-lg bg-gray-50">
                <p>Unable to load image from URL</p>
                <p class="text-xs mt-1 break-all">{{ $url }}</p>
            </div>
        @else
            {{-- Invalid URL format --}}
            <div class="text-sm text-gray-500 p-4 border border-gray-200 rounded-lg bg-gray-50">
                <p>Invalid image URL format</p>
                <p class="text-xs mt-1 break-all">{{ $url }}</p>
            </div>
        @endif
    </div>
@else
    <div class="text-sm text-gray-400 italic">
        No screenshot available
    </div>
@endif
